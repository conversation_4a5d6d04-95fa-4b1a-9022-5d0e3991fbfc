# -*- coding: utf-8 -*-

import sys
import pandas as pd
import numpy as np
import os
import json
import re
from pathlib import Path
from PyQt6.QtCore import QRectF # Add QRectF import

try:
    from PyQt6.QtWidgets import (
        QDialog, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
        QTextEdit, QTableWidget, QTableWidgetItem, QHeaderView,
        QGroupBox, QFileDialog, QMessageBox, QInputDialog, QTreeWidget,
        QTreeWidgetItem, QSlider, QFrame, QScrollArea, QSizePolicy
    )
    from PyQt6.QtCore import Qt, pyqtSignal
    from PyQt6.QtGui import QFont, QPixmap, QPainter, QPen, QBrush, QColor
    # PIL is not directly used for QPixmap display but might be for initial load if QPixmap fails for some formats
    # from PIL import Image, ImageTk # ImageTk is for Tkinter
except ImportError:
    print("错误: 未安装PyQt6库")
    print("请运行: pip install PyQt6 pillow")
    sys.exit(1)


class ImageCanvas(QLabel):
    """可缩放和拖拽的图片画布"""
    point_added = pyqtSignal(int, int, str)  # x, y (image coords), name
    point_dragged = pyqtSignal(str, int, int) # name, new_x (image coords), new_y (image coords)
    point_selected = pyqtSignal(str) # name of selected point

    def __init__(self):
        super().__init__()
        self.setMinimumSize(600, 400)
        self.setStyleSheet("border: 1px solid gray; background-color: lightgray;")
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setMouseTracking(True) # Important for some mouse events if needed

        # 图片相关
        self.original_pixmap = None
        self.zoom_factor = 1.0
        self.pan_offset_x = 0.0 # Use floats for precision
        self.pan_offset_y = 0.0 # Use floats for precision
        self.last_pan_pos = None # Stores QPointF for panning
        self.is_panning = False # CRITICAL: Ensure this is initialized

        # 温度点
        self.temp_points = {}  # {name: {'x': img_x, 'y': img_y, 'value': value}}

        # 点拖拽相关
        self.dragging_point_name = None
        self.selected_point_name = None # For highlighting or context operations

        self.setText("请导入背景图片")

    def get_point_at_position(self, screen_pos):
        """检查指定屏幕坐标是否有温度点. screen_pos is QPointF."""
        rect_size = 20 # Visual size of point rectangle / 2 for click detection radius
        for name, point_data in self.temp_points.items():
            # Convert stored image center to screen center
            point_screen_center_x = point_data['x'] * self.zoom_factor + self.pan_offset_x
            point_screen_center_y = point_data['y'] * self.zoom_factor + self.pan_offset_y

            # Check if screen_pos is within the bounding box of the point's visual representation
            if (abs(screen_pos.x() - point_screen_center_x) <= rect_size / 2 and
                abs(screen_pos.y() - point_screen_center_y) <= rect_size / 2):
                return name
        return None

    def screen_to_image_coords(self, screen_x, screen_y):
        """将屏幕坐标 (QLabel widget coords) 转换为图片上点中心的坐标 (original image coords)"""
        if not self.original_pixmap or self.zoom_factor == 0:
            return screen_x, screen_y
        image_x = (screen_x - self.pan_offset_x) / self.zoom_factor
        image_y = (screen_y - self.pan_offset_y) / self.zoom_factor
        return image_x, image_y

    def image_to_screen_coords(self, image_x, image_y):
        """将图片上点中心的坐标 (original image coords) 转换为屏幕坐标 (QLabel widget coords)"""
        if not self.original_pixmap:
            return image_x, image_y
        screen_x = image_x * self.zoom_factor + self.pan_offset_x
        screen_y = image_y * self.zoom_factor + self.pan_offset_y
        return screen_x, screen_y

    def get_color_for_value(self, value):
        """根据数值获取颜色梯度 - 蓝色到红色"""
        if value is None or value == 'N/A' or value == '':
            return QColor(128, 128, 128)

        try:
            current_value_float = float(value)
        except (ValueError, TypeError):
            return QColor(128, 128, 128)

        valid_values = []
        for p_data in self.temp_points.values(): # Changed p to p_data for clarity
            pval = p_data.get('value') # Use .get for safety
            if pval is not None and pval != 'N/A' and pval != '':
                try:
                    valid_values.append(float(pval))
                except (ValueError, TypeError):
                    continue

            if not valid_values: # No valid numeric values to compare against
                return QColor(150, 150, 255) # Default color if only one point or no comparable values

            min_val = min(valid_values)
            max_val = max(valid_values)

            if max_val == min_val: # All valid points have the same value
                 # Check if the current value is that same value
                if current_value_float == min_val: # or value == max_val
                    return QColor(128, 0, 128) # Purple for points when all have same value
                else: # Should not happen if value is one of the valid_values
                    return QColor(128,128,128)


            normalized = (current_value_float - min_val) / (max_val - min_val) if (max_val - min_val) != 0 else 0.5
            normalized = max(0.0, min(1.0, normalized)) # Clamp between 0 and 1

            if normalized <= 0.5:
                ratio = normalized * 2
                red = 0
                green = int(255 * ratio)
                blue = int(255 * (1 - ratio))
            else:
                ratio = (normalized - 0.5) * 2
                red = int(255 * ratio)
                green = int(255 * (1 - ratio))
                blue = 0
            return QColor(red, green, blue)
        except Exception as e:
            print(f"颜色计算错误: {e}, value: {value}, type: {type(value)}")
            return QColor(128, 128, 128)

    def load_image(self, image_path):
        """加载图片"""
        try:
            pixmap = QPixmap(image_path)
            if pixmap.isNull():
                QMessageBox.critical(self, "错误", f"无法加载图片: QPixmap为空 {image_path}")
                self.original_pixmap = None
                self.update_display()
                return False
            self.original_pixmap = pixmap
            self.zoom_factor = 1.0
            self.pan_offset_x = 0.0
            self.pan_offset_y = 0.0
            self.temp_points.clear() # Clear points when new image is loaded
            self.selected_point_name = None
            self.update_display()
            return True
        except Exception as e: # Catch any other unforeseen errors
            QMessageBox.critical(self, "错误", f"加载图片时发生未知错误: {str(e)}")
            self.original_pixmap = None
            self.update_display() # Update to show placeholder text
            return False

    def update_display(self):
        """更新显示"""
        if self.original_pixmap is None or self.original_pixmap.isNull():
            self.setText("请导入背景图片")
            # self.clear() # Clear may remove the text as well, setText should suffice
            return

        if self.width() <= 0 or self.height() <= 0:
            return

        self.setText("") # Clear placeholder text if image is loaded
        display_pixmap = QPixmap(self.size())
        display_pixmap.fill(Qt.GlobalColor.lightGray)
        painter = QPainter(display_pixmap)

        source_rect_x = -self.pan_offset_x / self.zoom_factor
        source_rect_y = -self.pan_offset_y / self.zoom_factor
        source_rect_width = self.width() / self.zoom_factor
        source_rect_height = self.height() / self.zoom_factor

        source_rect = QRectF(source_rect_x, source_rect_y, source_rect_width, source_rect_height)
        target_rect = QRectF(0.0, 0.0, float(self.width()), float(self.height()))

        painter.drawPixmap(target_rect, self.original_pixmap, source_rect)

        rect_size = 20 # Smaller default point size
        for name, point_data in self.temp_points.items():
            screen_center_x, screen_center_y = self.image_to_screen_coords(point_data['x'], point_data['y'])

            if not (screen_center_x < -rect_size or screen_center_x > self.width() + rect_size or \
                    screen_center_y < -rect_size or screen_center_y > self.height() + rect_size):

                color = self.get_color_for_value(point_data.get('value'))
                pen_color = QColor(0,0,0)
                pen_width = 1
                if name == self.selected_point_name:
                    pen_color = QColor(255,165,0) # Orange for selected
                    pen_width = 2

                painter.setPen(QPen(pen_color, pen_width))
                painter.setBrush(QBrush(color))

                rect_top_left_x = screen_center_x - rect_size / 2.0
                rect_top_left_y = screen_center_y - rect_size / 2.0

                painter.drawRect(QRectF(rect_top_left_x, rect_top_left_y, float(rect_size), float(rect_size)))

                painter.setPen(QPen(QColor(0,0,0), 1))
                text_x = rect_top_left_x + 2
                text_y_name = rect_top_left_y + rect_size * 0.4 # Adjusted for smaller rect
                text_y_value = rect_top_left_y + rect_size * 0.8

                font = painter.font()
                font.setPointSize(max(6, int(8 * self.zoom_factor))) # Scale font size roughly with zoom
                painter.setFont(font)

                painter.drawText(int(text_x), int(text_y_name), name)

                value = point_data.get('value', 'N/A')
                value_text = ""
                if isinstance(value, (float, np.floating)): # Check for numpy floats too
                    value_text = f"{value:.1f}" # One decimal place
                elif value is not None and value != 'N/A': # Check for None and 'N/A' string
                    value_text = str(value)
                else:
                    value_text = "N/A"
                painter.drawText(int(text_x), int(text_y_value), value_text)
        painter.end()
        self.setPixmap(display_pixmap)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if self.original_pixmap is None or self.original_pixmap.isNull(): # Check if pixmap is null
            return

        pos = event.position()
        clicked_point_name = self.get_point_at_position(pos)

        if event.button() == Qt.MouseButton.LeftButton:
            self.selected_point_name = clicked_point_name # Select or deselect
            if clicked_point_name:
                self.dragging_point_name = clicked_point_name
                self.last_pan_pos = pos # Store initial position for dragging
                self.point_selected.emit(clicked_point_name)
            else:
                # Left-click on empty space: Add new point
                self.add_temperature_point(pos.x(), pos.y())
            self.update_display()

        elif event.button() == Qt.MouseButton.RightButton:
            if clicked_point_name:
                self.selected_point_name = clicked_point_name # Select on right click as well
                self.dragging_point_name = clicked_point_name
                self.last_pan_pos = pos # Store initial position for dragging
                self.point_selected.emit(clicked_point_name)
            else: # Right-click on empty space
                self.is_panning = True # Correctly set is_panning
                self.last_pan_pos = pos
            self.update_display()


    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        pos = event.position()
        if self.dragging_point_name and self.dragging_point_name in self.temp_points:
            # Dragging an existing point
            if self.last_pan_pos is not None : # Ensure last_pan_pos is set
                screen_dx = pos.x() - self.last_pan_pos.x()
                screen_dy = pos.y() - self.last_pan_pos.y()

                img_dx = screen_dx / self.zoom_factor
                img_dy = screen_dy / self.zoom_factor

                self.temp_points[self.dragging_point_name]['x'] += img_dx
                self.temp_points[self.dragging_point_name]['y'] += img_dy

                self.last_pan_pos = pos # Update last position for next delta
                self.update_display()
                self.point_dragged.emit(self.dragging_point_name,
                                        int(self.temp_points[self.dragging_point_name]['x']),
                                        int(self.temp_points[self.dragging_point_name]['y']))

        elif self.is_panning and self.original_pixmap is not None and not self.original_pixmap.isNull(): # Check is_panning
            if self.last_pan_pos is not None:
                dx = pos.x() - self.last_pan_pos.x()
                dy = pos.y() - self.last_pan_pos.y()
                self.pan_offset_x += dx
                self.pan_offset_y += dy
                self.last_pan_pos = pos
                self.update_display()

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if self.dragging_point_name: # If a point was being dragged
             if event.button() == Qt.MouseButton.LeftButton or event.button() == Qt.MouseButton.RightButton:
                self.dragging_point_name = None

        if self.is_panning and event.button() == Qt.MouseButton.RightButton: # Reset panning only on right button release
            self.is_panning = False

        self.last_pan_pos = None

    def wheelEvent(self, event):
        """鼠标滚轮事件 - 缩放"""
        if self.original_pixmap is not None and not self.original_pixmap.isNull():
            delta = event.angleDelta().y()
            zoom_factor_delta = 1.1 if delta > 0 else (1.0 / 1.1)

            mouse_pos = event.position()

            img_coord_x_before_zoom = (mouse_pos.x() - self.pan_offset_x) / self.zoom_factor
            img_coord_y_before_zoom = (mouse_pos.y() - self.pan_offset_y) / self.zoom_factor

            new_zoom_factor = self.zoom_factor * zoom_factor_delta
            new_zoom_factor = max(0.05, min(20.0, new_zoom_factor))

            self.pan_offset_x = mouse_pos.x() - (img_coord_x_before_zoom * new_zoom_factor)
            self.pan_offset_y = mouse_pos.y() - (img_coord_y_before_zoom * new_zoom_factor)
            self.zoom_factor = new_zoom_factor

            self.update_display()

    def add_temperature_point(self, screen_x, screen_y):
        """添加温度点. screen_x, screen_y are widget (QLabel) coordinates."""
        if self.original_pixmap is None or self.original_pixmap.isNull():
            QMessageBox.warning(self, "无图片", "请先导入背景图片。")
            return

        img_x, img_y = self.screen_to_image_coords(screen_x, screen_y)

        point_name_suggestion = f"P{len(self.temp_points)+1}"
        loop_guard = 0 # Prevent infinite loop if name generation is flawed
        while point_name_suggestion in self.temp_points and loop_guard < 100:
            point_name_suggestion = f"P{len(self.temp_points)+1}_{np.random.randint(100)}"
            loop_guard +=1
        if loop_guard >= 100:
             QMessageBox.warning(self, "错误", "无法生成唯一的点名称。")
             return


        point_name, ok = QInputDialog.getText(
            self,
            "设置测温点名称",
            "请输入测温点名称 (对应映射配置中的逻辑名):",
            text=point_name_suggestion
        )

        if ok and point_name:
            if not point_name.strip():
                QMessageBox.warning(self, "名称无效", "测温点名称不能为空。")
                return
            if point_name in self.temp_points:
                QMessageBox.warning(self, "名称已存在", f"测温点名称 '{point_name}' 已存在。请使用其他名称。")
                return

            self.temp_points[point_name] = {
                'x': img_x,
                'y': img_y,
                'value': 'N/A'
            }
            self.selected_point_name = point_name
            self.update_display()
            self.point_added.emit(int(img_x), int(img_y), point_name)


    def remove_point(self, point_name):
        """删除温度点"""
        if point_name in self.temp_points:
            del self.temp_points[point_name]
            if self.selected_point_name == point_name:
                self.selected_point_name = None
            self.update_display()

    def update_point_value(self, point_name, value):
        """更新温度点数值"""
        if point_name in self.temp_points:
            self.temp_points[point_name]['value'] = value


class SensorLayoutDialog(QDialog):
    """温感布置分析窗口 - 严格参考sensor_layout.py"""

    def __init__(self, parent, app_data):
        super().__init__(parent)
        self.parent = parent
        self.app_data = app_data
        self.setWindowTitle("温感布置分析")
        self.setGeometry(200, 200, 1000, 750)
        self.setModal(True)

        self.df_imported = None
        self.data_mapping_config = {}

        self.display_mode = "slider"
        self.current_time_index = 0
        self.temp_points_data = {}
        self.image_path = None

        self.init_ui()
        self.load_data_and_mappings()
        self.update_time_slider_config()


    def load_data_and_mappings(self):
        """Load data from app_data and process initial mappings for existing points."""
        current_data_source = self.app_data.filtered_data if self.app_data.filter_applied and self.app_data.filtered_data is not None else self.app_data.imported_data
        self.df_imported = current_data_source.copy() if current_data_source is not None else None # Use a copy

        config_from_app = getattr(self.app_data, 'data_mapping_config', {})
        if isinstance(config_from_app, dict):
            self.data_mapping_config = config_from_app
        else:
            self.data_mapping_config = {}
            print("Warning: app_data.data_mapping_config is not a dictionary.")

        if self.df_imported is None:
            if not self.isHidden(): # Only show message if dialog is visible
                QMessageBox.warning(self, "无数据", "主数据未导入或为空。部分功能可能受限。")

        for name in list(self.temp_points_data.keys()): # Iterate over copy of keys
            self._update_point_series(name)

        self.calculate_and_redraw_based_on_mode()


    def init_ui(self):
        """初始化用户界面 - 参考原始_create_widgets"""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(5, 5, 5, 5)

        # 工具栏
        toolbar_layout = QHBoxLayout()

        import_btn = QPushButton("导入图片")
        import_btn.clicked.connect(self.import_image_ui)
        toolbar_layout.addWidget(import_btn)

        delete_btn = QPushButton("删除选中点")
        delete_btn.clicked.connect(self.delete_selected_point_ui)
        toolbar_layout.addWidget(delete_btn)

        separator1 = QFrame()
        separator1.setFrameShape(QFrame.Shape.VLine)
        toolbar_layout.addWidget(separator1)

        delta_btn = QPushButton("显示温差")
        delta_btn.clicked.connect(lambda: self.set_summary_display_mode_ui("delta_t"))
        toolbar_layout.addWidget(delta_btn)

        max_btn = QPushButton("最大值")
        max_btn.clicked.connect(lambda: self.set_summary_display_mode_ui("max_val"))
        toolbar_layout.addWidget(max_btn)

        min_btn = QPushButton("最小值")
        min_btn.clicked.connect(lambda: self.set_summary_display_mode_ui("min_val"))
        toolbar_layout.addWidget(min_btn)

        slider_mode_btn = QPushButton("时间点值")
        slider_mode_btn.clicked.connect(lambda: self.set_summary_display_mode_ui("slider"))
        toolbar_layout.addWidget(slider_mode_btn)

        separator2 = QFrame()
        separator2.setFrameShape(QFrame.Shape.VLine)
        toolbar_layout.addWidget(separator2)

        save_btn = QPushButton("保存项目")
        save_btn.clicked.connect(self.save_project_ui)
        toolbar_layout.addWidget(save_btn)

        load_btn = QPushButton("加载项目")
        load_btn.clicked.connect(self.load_project_ui)
        toolbar_layout.addWidget(load_btn)

        toolbar_layout.addStretch()

        return_btn = QPushButton("返回主页")
        return_btn.clicked.connect(self.accept)
        toolbar_layout.addWidget(return_btn)

        main_layout.addLayout(toolbar_layout)

        content_layout = QHBoxLayout()

        self.image_canvas = ImageCanvas()
        self.image_canvas.point_added.connect(self.on_point_added_canvas)
        self.image_canvas.point_selected.connect(self.on_point_selected_canvas)
        self.image_canvas.point_dragged.connect(self.on_point_dragged_canvas)
        content_layout.addWidget(self.image_canvas, 3)

        right_panel = QFrame()
        right_panel.setFixedWidth(300)
        right_layout = QVBoxLayout(right_panel)

        self.tree = QTreeWidget()
        self.tree.setHeaderLabels(["温度点", "值", "映射CSV列"])
        self.tree.setColumnWidth(0, 100)
        self.tree.setColumnWidth(1, 70)
        self.tree.itemSelectionChanged.connect(self.on_tree_selection_changed)
        right_layout.addWidget(self.tree)

        value_group = QGroupBox("当前数值显示")
        value_layout = QVBoxLayout(value_group)
        self.current_value_label = QLabel("选择温度点查看实时数值")
        self.current_value_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.current_value_label.setStyleSheet("border: 1px solid gray; padding: 10px; background-color: white;")
        value_layout.addWidget(self.current_value_label)
        right_layout.addWidget(value_group)

        content_layout.addWidget(right_panel)
        main_layout.addLayout(content_layout)

        slider_frame = QFrame()
        slider_layout = QHBoxLayout(slider_frame)
        slider_layout.addWidget(QLabel("时间点:"))
        self.time_slider = QSlider(Qt.Orientation.Horizontal)
        self.time_slider.setMinimum(0)
        self.time_slider.setMaximum(0)
        self.time_slider.valueChanged.connect(self.on_time_slider_value_changed)
        slider_layout.addWidget(self.time_slider)
        self.time_label = QLabel("时间索引: N/A")
        slider_layout.addWidget(self.time_label)
        main_layout.addWidget(slider_frame)

    def import_image_ui(self):
        file_types = "图片文件 (*.jpg *.jpeg *.png *.bmp *.gif);;所有文件 (*.*)"
        file_path, _ = QFileDialog.getOpenFileName(self, "选择背景图片", "", file_types)
        if file_path:
            if self.image_canvas.load_image(file_path):
                self.image_path = file_path
                self.temp_points_data.clear()
                self.update_treeview()
                self.calculate_and_redraw_based_on_mode()

    def delete_selected_point_ui(self):
        if self.image_canvas.selected_point_name:
            point_name = self.image_canvas.selected_point_name
            if point_name in self.temp_points_data:
                del self.temp_points_data[point_name]
            self.image_canvas.remove_point(point_name)
            self.update_treeview()
            self.current_value_label.setText("选择温度点查看实时数值")
        else:
            QMessageBox.warning(self, "无选中点", "请先在图片上选择一个点。")

    def on_point_added_canvas(self, img_x, img_y, name): # name is logical_name
        self.temp_points_data[name] = {
            "coords": (img_x, img_y), "mapped_csv_col": None,
            "logical_name": name, "display_value": np.nan, "raw_series": None
        }
        self._update_point_series(name)
        self.update_treeview()
        self.calculate_and_redraw_based_on_mode()
        for i in range(self.tree.topLevelItemCount()):
            item = self.tree.topLevelItem(i)
            if item.text(0) == name:
                self.tree.setCurrentItem(item)
                break

    def on_point_selected_canvas(self, name):
        self.image_canvas.selected_point_name = name
        for i in range(self.tree.topLevelItemCount()):
            item = self.tree.topLevelItem(i)
            if item.text(0) == name:
                self.tree.setCurrentItem(item)
                break
        self.update_current_value_label(name)

    def on_point_dragged_canvas(self, name, new_img_x, new_img_y):
        if name in self.temp_points_data:
            self.temp_points_data[name]['coords'] = (new_img_x, new_img_y)

    def _update_point_series(self, point_name):
        if point_name not in self.temp_points_data: return
        point_info = self.temp_points_data[point_name]
        mapped_col = None
        series = None
        current_df = self.app_data.filtered_data if self.app_data.filter_applied and self.app_data.filtered_data is not None else self.app_data.imported_data

        if current_df is not None and self.data_mapping_config:
            mappings = self.data_mapping_config.get('mappings', {})
            for csv_col, logical_name_in_map in mappings.items():
                if logical_name_in_map == point_name:
                    if csv_col in current_df.columns:
                        mapped_col = csv_col
                        series = current_df[csv_col]
                    else:
                        print(f"警告: 为逻辑名 '{point_name}' 映射的CSV列 '{csv_col}' 在数据中未找到。")
                        point_info["mapped_csv_col"] = f"{csv_col} (数据中不存在!)"
                    break
            if not mapped_col: # If point_name was not found as a logical_name in mappings
                 if point_name in current_df.columns: # Check if point_name itself is a CSV column
                     mapped_col = point_name
                     series = current_df[point_name]
                     print(f"信息: 点 '{point_name}' 直接用作CSV列名。")
                 else:
                     print(f"警告: 点 '{point_name}' 无有效映射且自身非CSV列名。")

        point_info["mapped_csv_col"] = mapped_col
        point_info["raw_series"] = series


    def ask_csv_column_mapping(self, available_cols, point_name):
        # This method might be redundant if on_point_added directly uses mapping config
        # Or could be used if a point name doesn't match any logical name in mapping
        pass # Placeholder, review if needed

    def update_treeview(self):
        self.tree.clear()
        for name, data in self.temp_points_data.items():
            val_str = "N/A"
            display_val = data.get('display_value')
            if pd.notnull(display_val):
                 if isinstance(display_val, (float, np.floating)):
                    val_str = f"{display_val:.2f}"
                 else:
                    val_str = str(display_val)

            col_str = data.get('mapped_csv_col')
            if col_str is None : col_str = "未映射"
            elif isinstance(col_str, str) and "(数据中不存在!)" in col_str:
                pass # Keep the error message
            elif col_str not in (self.df_imported.columns if self.df_imported is not None else []):
                 col_str = f"{col_str} (列缺失?)"


            item = QTreeWidgetItem([name, val_str, col_str])
            self.tree.addTopLevelItem(item)
            if name == self.image_canvas.selected_point_name:
                self.tree.setCurrentItem(item)

    def on_tree_selection_changed(self):
        current_item = self.tree.currentItem()
        if current_item:
            point_name = current_item.text(0)
            self.image_canvas.selected_point_name = point_name
            self.update_current_value_label(point_name)
            self.image_canvas.update_display()
        else:
            self.image_canvas.selected_point_name = None
            self.current_value_label.setText("选择温度点查看实时数值")
            self.image_canvas.update_display()

    def update_current_value_label(self, point_name):
        if point_name in self.temp_points_data:
            data = self.temp_points_data[point_name]
            value = data.get('display_value')
            val_text = "N/A"
            if pd.notnull(value):
                if isinstance(value, (float, np.floating)):
                    val_text = f"{value:.2f}"
                else:
                    val_text = str(value)
            self.current_value_label.setText(f"{point_name}: {val_text}")
        else:
            self.current_value_label.setText("选择温度点查看实时数值")

    def calculate_and_redraw_based_on_mode(self):
        current_df = self.app_data.filtered_data if self.app_data.filter_applied and self.app_data.filtered_data is not None else self.app_data.imported_data
        if current_df is None:
            for name in list(self.temp_points_data.keys()):
                 self.temp_points_data[name]["display_value"] = np.nan
                 self.image_canvas.update_point_value(name, np.nan)
            self.update_treeview()
            self.image_canvas.update_display() # Update canvas to show N/A
            return

        if self.display_mode == "slider":
            self.on_time_slider_value_changed(self.time_slider.value())
            return

        for name, point_info in self.temp_points_data.items():
            series = point_info.get("raw_series")
            value_to_display = np.nan
            if series is not None and not series.empty:
                numeric_series = pd.to_numeric(series, errors='coerce').dropna()
                if not numeric_series.empty:
                    if self.display_mode == "delta_t":
                        value_to_display = numeric_series.iloc[-1] - numeric_series.iloc[0] if len(numeric_series) >= 2 else np.nan
                    elif self.display_mode == "max_val":
                        value_to_display = numeric_series.max()
                    elif self.display_mode == "min_val":
                        value_to_display = numeric_series.min()
            point_info["display_value"] = value_to_display
            self.image_canvas.update_point_value(name, value_to_display)
        self.update_treeview()
        self.image_canvas.update_display()

    def set_summary_display_mode_ui(self, mode):
        self.display_mode = mode
        is_slider_mode = (mode == "slider")
        self.time_slider.setEnabled(is_slider_mode)

        if is_slider_mode:
            self.update_time_slider_config()
        # This will call on_time_slider_value_changed if mode is slider and slider value changes,
        # or directly calculate for other modes.
        self.calculate_and_redraw_based_on_mode()


    def on_time_slider_value_changed(self, value_idx):
        self.current_time_index = value_idx
        # self.display_mode = "slider" # Already set by set_summary_display_mode_ui

        time_label_text = f"时间索引: {self.current_time_index}"
        current_df = self.app_data.filtered_data if self.app_data.filter_applied and self.app_data.filtered_data is not None else self.app_data.imported_data

        if current_df is not None and not current_df.empty:
            if self.current_time_index < len(current_df):
                time_col_name_from_config = self.data_mapping_config.get('time_column') # This is the CSV header name

                if time_col_name_from_config and time_col_name_from_config in current_df.columns:
                    try:
                        actual_time_val = current_df[time_col_name_from_config].iloc[self.current_time_index]
                        time_label_text += f" ({actual_time_val})"
                    except IndexError:
                         pass
                for name, point_info in self.temp_points_data.items():
                    series = point_info.get("raw_series")
                    value_at_time = np.nan
                    if series is not None and not series.empty:
                        numeric_series = pd.to_numeric(series, errors='coerce')
                        if self.current_time_index < len(numeric_series): # Check against series length
                            value_at_time = numeric_series.iloc[self.current_time_index]
                    point_info["display_value"] = value_at_time
                    self.image_canvas.update_point_value(name, value_at_time)
                self.update_treeview()
                self.image_canvas.update_display()
        self.time_label.setText(time_label_text)

    def update_time_slider_config(self):
        current_df = self.app_data.filtered_data if self.app_data.filter_applied and self.app_data.filtered_data is not None else self.app_data.imported_data
        if current_df is not None and not current_df.empty:
            max_index = len(current_df) - 1
            self.time_slider.setMaximum(max_index if max_index >=0 else 0)
            new_time_index = self.current_time_index
            if new_time_index > max_index : new_time_index = max_index if max_index >=0 else 0
            if new_time_index < 0 : new_time_index = 0

            self.current_time_index = new_time_index
            self.time_slider.setValue(self.current_time_index)
            self.time_slider.setEnabled(True)
            if self.display_mode == "slider":
                 self.on_time_slider_value_changed(self.current_time_index)
        else:
            self.time_slider.setMaximum(0)
            self.time_slider.setValue(0)
            self.current_time_index = 0
            self.time_slider.setEnabled(False)
            self.time_label.setText("时间索引: N/A")

    def save_project_ui(self):
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存项目", "", "温感布置项目 (*.slt_proj.json);;所有文件 (*.*)"
        )
        if not file_path: return
        if not file_path.lower().endswith('.slt_proj.json'):
            file_path += '.slt_proj.json'
        try:
            project_points_data = {}
            for name, canvas_point_visual_data in self.image_canvas.temp_points.items():
                dialog_point_actual_data = self.temp_points_data.get(name, {})
                project_points_data[name] = {
                    "coords": (canvas_point_visual_data['x'], canvas_point_visual_data['y']),
                    "mapped_csv_col": dialog_point_actual_data.get("mapped_csv_col"),
                    # "logical_name": name # Key is already logical name
                }
            project_data = {
                "image_path": self.image_path,
                "csv_filepath": getattr(self.app_data, 'imported_filepath', None),
                "temp_points": project_points_data,
                "view_state": {
                    "zoom_factor": self.image_canvas.zoom_factor,
                    "pan_offset_x": self.image_canvas.pan_offset_x,
                    "pan_offset_y": self.image_canvas.pan_offset_y,
                    "display_mode": self.display_mode,
                    "current_time_index": self.current_time_index,
                    "selected_point_name": self.image_canvas.selected_point_name
                }
            }
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2)
            QMessageBox.information(self, "成功", "项目已保存。")
        except Exception as e:
            QMessageBox.critical(self, "保存失败", f"无法保存项目: {str(e)}\n{traceback.format_exc()}")

    def load_project_ui(self):
        file_path, _ = QFileDialog.getOpenFileName(
            self, "加载项目", "", "温感布置项目 (*.slt_proj.json);;所有文件 (*.*)"
        )
        if not file_path: return
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                proj_data = json.load(f)

            self.load_data_and_mappings()

            self.image_path = proj_data.get("image_path")
            if self.image_path and os.path.exists(self.image_path):
                self.image_canvas.load_image(self.image_path)
            elif self.image_path:
                QMessageBox.warning(self, "图片缺失", f"图片 '{self.image_path}' 未找到。画布将为空。")
                self.image_canvas.original_pixmap = None

            self.temp_points_data.clear()
            self.image_canvas.temp_points.clear()

            loaded_temp_points_config = proj_data.get("temp_points", {})
            for point_logical_name, data_from_file in loaded_temp_points_config.items():
                coords = tuple(data_from_file.get("coords", (0,0)))
                self.image_canvas.temp_points[point_logical_name] = {
                    'x': coords[0], 'y': coords[1], 'value': 'N/A'
                }
                self.temp_points_data[point_logical_name] = {
                    "coords": coords,
                    "mapped_csv_col": data_from_file.get("mapped_csv_col"),
                    # "logical_name": point_logical_name, # Key is logical name
                    "display_value": np.nan, "raw_series": None
                }
                self._update_point_series(point_logical_name) # This is crucial to link data series

            vs = proj_data.get("view_state", {})
            self.image_canvas.zoom_factor = vs.get("zoom_factor", 1.0)
            self.image_canvas.pan_offset_x = vs.get("pan_offset_x", 0.0)
            self.image_canvas.pan_offset_y = vs.get("pan_offset_y", 0.0)
            self.display_mode = vs.get("display_mode", "slider")
            self.current_time_index = vs.get("current_time_index", 0)
            self.image_canvas.selected_point_name = vs.get("selected_point_name")

            self.update_time_slider_config()
            self.set_summary_display_mode_ui(self.display_mode)

            self.update_treeview() # Final sync of tree
            self.image_canvas.update_display() # Final redraw of canvas

            QMessageBox.information(self, "成功", "项目已加载。")
        except Exception as e:
            QMessageBox.critical(self, "加载失败", f"无法加载项目: {str(e)}\n{traceback.format_exc()}")

# 为了兼容主程序调用
def show_sensor_layout_window(parent, app_data):
    dialog = SensorLayoutDialog(parent, app_data)
    dialog.exec()
