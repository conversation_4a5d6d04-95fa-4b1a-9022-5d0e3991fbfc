# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import pandas as pd
import re
import os
import matplotlib.pyplot as plt

# 导入其他功能模块窗口
# 注意：您需要确保这些 .py 文件与本文件在同一目录下
try:
    from sensor_layout import SensorLayoutWindow
    from export_report import ExportReportWindow
    # 下面是缺失的模块，我们先用占位符窗口代替，以确保程序能运行
    from data_mapping import MappingWindow
    from data_filtering import FilteringWindow
    from charging_analysis import ChargingAnalysisWindow
except ImportError as e:
    messagebox.showerror("模块导入错误", f"无法导入功能模块: {e}\n\n请确保 sensor_layout.py, export_report.py, data_mapping.py, data_filtering.py, 和 charging_analysis.py 文件与主程序在同一个文件夹中。")
    exit()

# 配置 Matplotlib 以支持中文显示
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']
    plt.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"注意: 无法设置 Matplotlib 默认中文字体: {e}")

class AppData:
    """应用的中央数据存储类"""
    def __init__(self):
        self.imported_data = None
        self.data_mapping_config = {}
        self.filtered_data_config = {}
        self.imported_filepath = None

class MainApplication(tk.Tk):
    """主应用程序窗口"""
    def __init__(self, app_data):
        super().__init__()
        self.app_data = app_data
        self.title("数据处理显示程序 (优化版)")
        self.geometry("800x600")

        # --- UI 布局 ---
        main_frame = ttk.Frame(self, padding="10")
        main_frame.pack(expand=True, fill=tk.BOTH)

        button_frame = ttk.Frame(main_frame)
        button_frame.pack(side=tk.LEFT, anchor=tk.NW, padx=10, pady=10)

        # 功能按钮配置
        buttons_config = [
            ("导入数据", self.open_import_data),
            ("数据映射", self.open_data_mapping),
            ("数据筛选", self.open_data_filtering),
            ("充电分析", self.open_charging_analysis),
            ("温感布置", self.open_sensor_layout),
            ("导出充电数据报告", self.open_export_report),
            ("数据分析 (占位)", self.open_data_analysis)
        ]
        for text, command in buttons_config:
            ttk.Button(button_frame, text=text, command=command, width=20).pack(pady=5, fill=tk.X)

        info_frame = ttk.LabelFrame(main_frame, text="导入数据显示区域", padding="10")
        info_frame.pack(side=tk.LEFT, expand=True, fill=tk.BOTH, padx=10, pady=10)
        
        self.info_text_area = tk.Text(info_frame, height=10, width=50, state=tk.DISABLED, wrap=tk.WORD)
        self.info_text_area.pack(expand=True, fill=tk.BOTH)

        self.update_info_display("应用程序已启动。请先导入CSV数据文件。")

    def update_info_display(self, message):
        """更新主界面的信息显示区域"""
        self.info_text_area.config(state=tk.NORMAL)
        self.info_text_area.delete(1.0, tk.END)
        self.info_text_area.insert(tk.END, message)
        self.info_text_area.config(state=tk.DISABLED)

    def open_import_data(self):
        """打开文件对话框以导入CSV数据"""
        messagebox.showinfo("CSV格式要求", "请选择一个CSV格式的数据文件...", parent=self)
        filepath = filedialog.askopenfilename(
            title="选择CSV数据文件",
            filetypes=(("CSV 文件", "*.csv"), ("所有文件", "*.*")),
            parent=self)
        
        if not filepath:
            self.update_info_display("用户取消了文件选择。")
            return

        try:
            # 尝试用不同的编码格式读取文件，增加兼容性
            try:
                df = pd.read_csv(filepath, skip_blank_lines=False)
            except UnicodeDecodeError:
                try:
                    df = pd.read_csv(filepath, encoding='gbk', skip_blank_lines=False)
                except UnicodeDecodeError:
                    df = pd.read_csv(filepath, encoding='latin1', skip_blank_lines=False)
            
            # 清理列名中的单位等无关字符
            df.columns = [re.sub(r'\[.*?\]', '', col).strip() for col in df.columns]
            # 删除全为空值的行
            df.dropna(how='all', inplace=True)
            df.reset_index(drop=True, inplace=True)

            # 更新共享数据
            self.app_data.imported_data = df
            self.app_data.imported_filepath = filepath
            
            nr, nc = df.shape
            col_preview = ', '.join(df.columns[:5])
            if nc > 5:
                col_preview += '...'
            
            info_msg = (f"成功导入: {os.path.basename(filepath)}\n"
                        f"数据维度: {nr} 行, {nc} 列\n"
                        f"前5个列名: {col_preview}")
            self.update_info_display(info_msg)
            messagebox.showinfo("成功", "数据导入和初步清理完成！", parent=self)
            
        except Exception as e:
            error_msg = f"导入文件时发生错误: {e}"
            messagebox.showerror("导入错误", error_msg, parent=self)
            self.update_info_display(error_msg)

    def check_data_before_opening_window(self, window_name):
        """在打开需要数据的窗口前检查数据是否已导入"""
        if self.app_data.imported_data is None:
            messagebox.showerror("数据缺失", f"请先导入数据文件，再进行“{window_name}”操作。", parent=self)
            return False
        return True

    def open_data_mapping(self):
        if self.check_data_before_opening_window("数据映射"):
            MappingWindow(self, self.app_data)

    def open_sensor_layout(self):
        if self.check_data_before_opening_window("温感布置"):
            # 检查Pillow库是否存在，因为此模块强依赖它
            try:
                from PIL import Image, ImageTk
            except ImportError:
                messagebox.showerror("依赖缺失", "Pillow (PIL) 库未安装或找不到。\n温感布置功能无法使用。\n请通过 'pip install Pillow' 命令安装。", parent=self)
                return
            SensorLayoutWindow(self, self.app_data)

    def open_data_filtering(self):
        if self.check_data_before_opening_window("数据筛选"):
            FilteringWindow(self, self.app_data)

    def open_charging_analysis(self):
        if self.check_data_before_opening_window("充电分析"):
            ChargingAnalysisWindow(self, self.app_data)

    def open_export_report(self):
        if self.check_data_before_opening_window("导出充电数据报告"):
            ExportReportWindow(self, self.app_data)

    def open_data_analysis(self):
        """这是一个占位符功能"""
        self.update_info_display("“数据分析”功能当前为占位符，尚未实现。")
        win = tk.Toplevel(self)
        win.title("数据分析 (待实现)")
        win.geometry("400x200")
        ttk.Label(win, text="此功能模块正在开发中...").pack(padx=20, pady=20, expand=True)
        ttk.Button(win, text="返回", command=win.destroy).pack(pady=10)
        win.transient(self)
        win.grab_set()

if __name__ == "__main__":
    # 实例化共享数据对象
    app_data_instance = AppData()
    # 启动主程序
    app = MainApplication(app_data_instance)
    app.mainloop()
