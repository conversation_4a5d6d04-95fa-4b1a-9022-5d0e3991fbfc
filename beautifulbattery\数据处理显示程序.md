# 目的

用一个程序实现多种数据显示方式，将数据显示及处理方式，集合在一个程序中



# 方法：

构建GUI来显示和交互式操作数据。



# 需求：

## 1.1 主页面

1. 主界面上应包含至少如下按钮：1.导入数据  2.数据映射  3.充电分析  4.温感布置 5.数据筛选 6.数据分析

2. 点击按钮“导入数据”，可导入本地数据，导入的数据应支持csv格式，且有文言提示，告知用户csv数据的格式要求。

3. 点击按钮“数据映射”，可进入“数据映射”界面

4. 点击按钮“充电分析”，可进入“充电分析”界面

5. 点击按钮“温感布置”，可进入“温感布置”界面

6. 点击按钮“数据筛选”，可进入“数据筛选”界面

7. 点击按钮“数据分析”，可进入“数据分析”界面

8. 点击按钮“导出充电数据报告”，可进入“导出充电数据报告”界面

9. 主界面上应有信息显示区域，可以显示历史导入过的数据，以及导入数据的关键信息，关键信息包括数据的时间，长度，电池初始温度等，可以后面再定义。



## 1.1 导入数据功能

1. 目的：通过对话框，可选择导入文件的位置，可导入本地数据，

2. 数据格式要求：导入的数据应支持csv格式，且有文言提示，告知用户csv数据的格式要求。

3. 导入后的数据，若表头含有\[\*],也就是有中括号，且中括号内有字符，应将其删除

4. 导入后的数据，若带有空白行，应将其删除。

5. 后续数据分析使用的数据应在采用导入数据的基础上，结合数据映射界面的数据来使用。



## 1.2 数据映射页面

目的：建立 csv中的表头数据与数据分析，充电分析，温感布置分析等数据分析使用数据的对应关系

1. ‘数据映射’ 应该是打开一个新的窗口，里面包含至少如下按钮，“导入配置”，‘保存配置’，‘应用及返回’

2. 表头数据和温度点数据的对应应该按行来写，一行一个表头数据和温度点名称

3. 数据映射表建立以后，可以通过点击‘保存配置’保存相应的配置。

4. 数据映射表上应包含‘导入配置’，‘保存配置’ 以及‘增加’和‘减少’按钮用于增加温度点映射行和减少温度点映射行。同时还应有返回键，用于返回主页面。

5. 其他界面进行数据处理分析或分析时，可以采用数据映射的数据或采用原始表头数据。



* 数据映射配置

```python
class MappingWindow(tk.Toplevel):
    def __init__(self, parent, current_mapping, headers, current_time_column=None):
        """创建映射配置窗口"""
        # 界面组件初始化...
        
    def add_row(self, csv_header_val=None, point_name_val=""):
        """添加新的映射关系行"""
        
    def save_config(self):
        """保存映射配置到JSON文件"""
        
    def load_config(self):
        """从JSON文件加载映射配置"""
```



6. 用户界面设计：

映射配置窗口

```plain&#x20;text
+-------------------------------------------------+
| 时间列: [Timestamp ▼]                            |
|                                                 |
| [增加映射] [删除最后一行]                        |
|                                                 |
| CSV列: [Sensor1_Temp ▼] 温度点名称: [Cell1]     |
| CSV列: [Sensor2_Temp ▼] 温度点名称: [Cell2]     |
|                                                 |
| [导入配置] [保存配置] [应用并返回]              |
+-------------------------------------------------+
```

## 1.3 充电分析页面

### 1.3.1 系统概述

本界面是一个用于电池充电数据分析的界面，主要功能包括：

* 导入充电基准数据，用文字提示用户，该基准数据为供应商提供的充电map

* 然后进行如下的数据映射：

* 根据导入的充电基准数据和主页面导入的数据以及数据映射的结果进行数据分析

* 执行复杂的电池充电特性分析

* 可视化分析结果（带温度基准线）

* 生成统计报告

* 保存分析结果



### 1.3.2 功能模块设计



1. 文件导入模块

```python
def select_charging_file(self):
    """选择充电基准文件（支持CSV和Excel格式）"""
    # 实现文件选择对话框
    # 更新界面状态


```



* 数据处理引擎

```python
def process_data(self):
    """核心数据处理流程（多线程执行）"""
    # 步骤1：加载基准数据
    # 步骤2：数据清洗（过滤异常温度值）
    # 步骤3：处理输入数据
    # 步骤4：创建温度标记列
    # 步骤5：电流计算（核心算法）
    # 步骤6：保存结果
```



* 温度标记算法

```python
def create_marker_column(self):
    """创建温度标记列（基于5℃区间）"""
    # 遍历输入数据
    # 检测温度跨越5的倍数边界
    # 标记边界点（a=1）
    # 创建温度区间标签（如"20-25℃"）
    # 计算温度基准值
```



* 电流计算算法

```python
def calculate_current(self, temp, voltage):
    """改进后的电流计算算法"""
    # 温度层选择（最接近的23个点）
    # 电压筛选（选择大于给定电压的第一个值）
    # 双温度层插值（上下各23个点）
    # 安全处理边界条件
    # 返回计算电流和使用电压
```



* 可视化模块

```python
def show_analysis_plot(self):
    """显示分析图表（带温度基准线）"""
    # 创建Matplotlib图表
    # 绘制计算电流曲线
    # 绘制实际电流曲线（如果存在）
    # 添加温度基准线（灰色虚线）
    # 标记温度阈值点（绿色圆点）
    # 添加统计信息框
    # 嵌入Tkinter界面
```



* 统计分析模块

```python
def generate_statistics(self):
    """生成温度区间统计信息"""
    # 按温度区间分组
    # 计算平均电流和标准差
    # 统计数据点数
    # 格式化统计文本
```



* 报告生成模块

```python
def generate_report(self):
    """生成分析报告（预留接口）"""
    # 目前为占位功能
    # 可扩展为PDF/Word报告生成
```



* 多线程控制

```python
def start_processing(self):
    """启动处理线程"""
    # 验证输入文件
    # 禁用操作按钮
    # 启动后台处理线程
```



* UI状态管理

```python
def toggle_buttons(self, state):
    """切换按钮状态（启用/禁用）"""

def update_progress(self, value, message=None):
    """更新进度条和状态信息"""

def update_status(self, message):
    """更新状态栏文本"""
```



### 1.3.3 系统架构设计



1. 分层架构

```plain&#x20;text
graph TD
    A[用户界面层] --> B[业务逻辑层]
    B --> C[数据访问层]
    
    subgraph A[用户界面层]
        A1[主窗口]
        A2[文件选择]
        A3[进度显示]
        A4[图表展示]
    end
    
    subgraph B[业务逻辑层]
        B1[数据处理]
        B2[温度标记]
        B3[电流计算]
        B4[统计分析]
        B5[可视化]
    end
    
    subgraph C[数据访问层]
        C1[CSV加载]
        C2[Excel加载]
        C3[结果保存]
    end
```



* 核心类设计



**ChargingAnalyzerPro类（主控制器）**

| 属性              | 说明                |
| --------------- | ----------------- |
| `charging_path` | 充电基准文件路径          |
| `df`            | 充电基准数据(DataFrame) |
| `progress`      | 进度条变量             |



| 方法                       | 说明       |
| ------------------------ | -------- |
| `process_data()`         | 核心数据处理流程 |
| `create_marker_column()` | 温度区间标记算法 |
| `calculate_current()`    | 电流计算核心算法 |
| `show_analysis_plot()`   | 结果可视化    |
| `generate_statistics()`  | 统计信息生成   |



### 1.3.4 关键算法分析



1. 温度标记算法

```python
# 创建温度标记列（a）
for i in range(len(input_df)):
    current_val = input_df['MinT'].iloc[i]
    current_base = (current_val // 5) * 5
    
    if i == 0:
        prev_base = -np.inf
    else:
        prev_val = input_df['MinT'].iloc[i-1]
        prev_base = (prev_val // 5) * 5
    
    # 标记条件：跨越5的倍数边界且当前值≥基准值
    condition = (current_val >= current_base) and (prev_base < current_base)
    a = 1 if condition else 0

# 创建温度区间标签
input_df['温度区间'] = input_df['MinT'].apply(
    lambda x: f"{int((x//5)*5)}-{int((x//5)*5+5)}℃"
)

# 计算温度基准值
input_df['温度基准值'] = input_df['MinT'].apply(
    lambda x: int((x//5)*5)
)
```



* 电流计算算法

```python
def calculate_current(temp, voltage):
    # 1. 温度层选择
    temp_filtered = df[df['temp'] <= temp]
    if len(temp_filtered) < 23:
        temp_filtered = df.nsmallest(23, 'temp')
    
    # 2. 选择最常见温度值
    common_temp = temp_filtered['temp'].mode()[0]
    temp_df = temp_filtered[temp_filtered['temp'] == common_temp]
    
    # 3. 电压筛选
    try:
        voltage_used = temp_df[temp_df['voltage'] > voltage].iloc[0]['voltage']
    except:
        voltage_used = temp_df['voltage'].max()
    
    # 4. 双温度层插值
    temp_below = df[df['temp'] <= temp].nlargest(23, 'temp')
    temp_above = df[df['temp'] > temp].nsmallest(23, 'temp')
    
    # 5. 安全边界处理
    if temp_below.empty or temp_above.empty:
        nearest = df.iloc[(df['temp'] - temp).abs().argsort()[0]]
        return nearest['current'], nearest['voltage']
    
    # 6. 线性插值
    current = np.interp(
        temp,
        [temp_below.iloc[0]['temp'], temp_above.iloc[0]['temp']],
        [temp_below.iloc[0]['current'], temp_above.iloc[0]['current']]
    )
    
    return current, voltage_used
```



### 1.3.5 可视化设计



1. 分析图表组件

```plain&#x20;text
graph LR
    A[主坐标轴] --> B[电流曲线]
    A --> C[温度基准线]
    A --> D[阈值标记点]
    A --> E[统计信息框]
    
    B --> B1[计算电流曲线]
    B --> B2[实际电流曲线]
    
    C --> C1[5℃间隔灰色虚线]
    
    D --> D1[绿色圆点标记]
    
    E --> E1[按温度区间分组统计]
```



图表元素说明

1. **主电流曲线**：蓝色实线表示计算电流，红色虚线表示实际电流（如果存在）

2. **温度基准线**：在5℃整数倍位置绘制灰色虚线（如0℃,5℃,10℃等）

3) **阈值标记点**：在温度跨越5℃倍数的位置标记绿色圆点

4) **统计信息框**：显示各温度区间的电流统计信息：

   * 平均电流

   * 电流标准差

   * 数据点数



### 1.3.6 设计特点



1. &#x20;多线程处理

```python
def start_processing(self):
    """启动处理线程"""
    self.toggle_buttons(False)
    threading.Thread(target=self.process_data, daemon=True).start()
```

* 使用独立线程执行数据处理，避免界面冻结

* 通过`update_progress`实现线程安全的状态更新



* 鲁棒性设计

- 数据清洗：过滤异常温度值（<-30或>65）

- 空值处理：`pd.to_numeric(..., errors='coerce')` + `dropna`

- 边界检查：电压/温度插值时的边界条件处理

- 异常捕获：关键操作使用try-except块



* 进度反馈机制

```python
def update_progress(self, value, message=None):
    self.progress.set(value)
    if message:
        self.status_label.config(text=message)
    self.root.update_idletasks()
```

* 分阶段进度更新（10%/30%/95%）

* 实时状态消息提示

* 调用`update_idletasks`确保界面及时刷新



* 中文支持

```python
# 配置Matplotlib中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']  # Windows
# plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']  # Mac
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
```





### 1.3.5  典型工作流程

1. **数据准备**：

   * 选择充电基准文件（包含temp/voltage/current的CSV）

   * 选择输入参数文件（包含MinT/MaxV的CSV），已有主页面的数据导入获取，且可通过数据映射，将数据转换成充电分析所需要的数据。

2. **执行分析**：

   * 点击"开始分析"按钮

   * 系统自动执行数据处理流程

   * 保存结果到CSV文件

3) **结果查看**：

   * 点击"显示图表"查看可视化分析

   * 分析图表包含电流曲线和温度基准线

   * 查看统计信息框中的分组数据

4) **报告生成**：

   * 点击"生成报告"导出分析结果（预留功能）





## 1.4 温感布置页面



目的：本工具是一个用于电池温度变化趋势分析的桌面应用程序，主要功能包括：

* 导入电池温度数据（CSV格式）

* 加载电池布局图像

* 在图像上标记温度监测点

* 建立数据列与温度点的映射关系

* 多模式温度数据分析（温差/最大值/最小值）

* 时间序列温度变化可视化

* 保存/加载分析项目



### 1.4.1 功能模块设计

1\. 图像处理模块

```python
def import_image(self):
    """加载电池布局图像"""
    filepath = filedialog.askopenfilename(filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp *.gif")])
    if filepath:
        self.original_image = Image.open(filepath)
        self.image_path = filepath
        self.display_image()
```



2\. 温度点管理

```python
def add_point_at(self, x, y):
    """在指定坐标添加温度点"""
    name = simpledialog.askstring("输入", "请输入温度点名称:", parent=self)
    if name:
        self.temp_points[name] = {'coords': (x,y), ...}
        self.draw_point(name)

def delete_selected_point(self):
    """删除选定的温度点"""
    if self.selected_point_name_in_tree:
        del self.temp_points[self.selected_point_name_in_tree]
        self._update_ui_states()
```



3\. 温度分析引擎

```python
def set_summary_display_mode(self, mode, force_recalc=False):
    """设置分析模式（温差/最大值/最小值）"""
    if mode == "delta_t":
        # 计算温差：终点温度 - 起点温度
        calculated_value = series.iloc[-1] - series.iloc[0]
    elif mode == "max_val":
        calculated_value = series.max()
    elif mode == "min_val":
        calculated_value = series.min()
```



4\. 时间序列分析

```python
def on_time_slider_change(self, value_str):
    """处理时间滑块变化事件"""
    self.current_time_index = int(float(value_str))
    self.update_display_for_slider()

def update_display_for_slider(self):
    """更新指定时间点的温度显示"""
    idx = self.current_time_index
    for name, point_data in self.temp_points.items():
        header = point_to_header.get(name)
        if header and header in self.df.columns and idx < len(self.df[header]):
            val = self.df[header].iloc[idx]
            point_data['slider_value'] = float(val)
```



5\. 项目持久化

```python
def save_project(self):
    """保存整个分析项目"""
    project_data = {
        "image_path": self.image_path,
        "csv_filepath": self.csv_filepath,
        "temp_points": self.temp_points,
        "mapping": self.mapping,
        # ...其他配置数据
    }
    with open(filepath, 'w') as f: 
        json.dump(project_data, f, indent=4)

def load_project(self):
    """加载分析项目"""
    with open(filepath, 'r') as f: 
        project_data = json.load(f)
    # 恢复项目状态...
```



### 1.4.2 系统架构设计

1\. 分层架构

```plain&#x20;text
graph TD
    A[用户界面层] --> B[业务逻辑层]
    B --> C[数据访问层]
    
    subgraph A[用户界面层]
        A1[图像画布]
        A2[结果树]
        A3[时间滑块]
    end
    
    subgraph B[业务逻辑层]
        B1[温度点管理]
        B2[温度分析]
        B3[项目持久化]
    end
    
    subgraph C[数据访问层]
        C1[CSV温度数据]
        C2[电池布局图像]
        C3[温度点配置]
        C4[项目文件]
    end
```



2\. 核心类设计



**MainApp类（主应用）**

| 属性               | 说明                   |
| ---------------- | -------------------- |
| `df`             | 存储温度数据的DataFrame     |
| `temp_points`    | 温度点字典{名称: {坐标, 计算值}} |
| `mapping`        | CSV列与温度点的映射关系        |
| `original_image` | 电池布局图像               |
| `zoom_factor`    | 图像缩放比例               |



| 方法                           | 说明        |
| ---------------------------- | --------- |
| `import_data()`              | 导入CSV温度数据 |
| `import_image()`             | 加载电池布局图像  |
| `set_summary_display_mode()` | 设置分析模式    |
| `save_project()`             | 保存分析项目    |
| `load_project()`             | 加载分析项目    |



**MappingWindow类（映射配置）**

| 方法              | 说明       |
| --------------- | -------- |
| `add_row()`     | 添加新的映射关系 |
| `remove_row()`  | 删除映射关系   |
| `save_config()` | 保存映射配置   |
| `load_config()` | 加载映射配置   |



### 1.4.3 关键算法

1\. 温度值颜色映射

```python
def get_color_for_value(value, min_val, max_val):
    """根据温度值生成渐变色（蓝到红）"""
    if max_val == min_val: 
        return "#0000FF"  # 单一值返回蓝色
    
    normalized = (value - min_val) / (max_val - min_val)
    red = int(normalized * 255)
    blue = int((1 - normalized) * 255)
    return f'#{red:02x}00{blue:02x}'
```

2\. 温度点渲染

```python
def draw_point(self, name):
    """渲染温度点到画布"""
    x, y = point_data['coords']
    scaled_x = x * self.zoom_factor
    scaled_y = y * self.zoom_factor
    
    # 计算显示颜色
    color = get_color_for_value(value_to_display, min_c, max_c)
    
    # 创建矩形框
    self.canvas.create_rectangle(
        scaled_x - radius_x, scaled_y - radius_y, 
        scaled_x + radius_x, scaled_y + radius_y,
        fill=color, outline="black"
    )
    
    # 添加温度点名称和值
    self.canvas.create_text(scaled_x, scaled_y, text=name)
    self.canvas.create_text(scaled_x, scaled_y + radius_y + 5, 
                          text=f"{prefix}: {value_to_display:.2f}")
```



### 1.4.4 数据存储设计

项目文件结构（JSON）

```json
{
  "image_path": "/data/battery_layout.png",
  "csv_filepath": "/data/temperature_data.csv",
  "temp_points": {
    "Cell1": {
      "coords": [120, 85],
      "last_calculated_value": 5.2,
      "slider_value": 32.5
    },
    "Cell2": {
      "coords": [210, 90],
      "last_calculated_value": 4.8,
      "slider_value": 31.8
    }
  },
  "mapping": {
    "Sensor1_Temp": "Cell1",
    "Sensor2_Temp": "Cell2"
  },
  "time_column_header": "Timestamp",
  "zoom_factor": 1.2,
  "display_mode": "delta_t",
  "current_time_index": 150
}
```

### 1.4.5 用户界面设计

温感布置界面布局

```plain&#x20;text
+-----------------------------------------+--------------------------+
| 工具栏 [导入图片]     |                          |
| [插入温度点][删除点][显示温差][最大值]   |      结果树              |
| [最小值][保存][加载]                    | +----------------------+ |
|                                         | | 温度点名称 | 值      | |
|                                         | +----------------------+ |
|                电池布局图像             | | Cell1     | 5.2°C  | |
|  (带温度点标记和颜色编码)               | | Cell2     | 4.8°C  | |
|                                         | +----------------------+ |
+-----------------------------------------+--------------------------+
| [时间] [===========时间滑块============]         |
+---------------------------------------------------------------------+
```



## 1.5 数据筛选页面

### 1.5.1 系统概述

该界面用于对‘导入数据功能’加载的数据CSV文件以及‘数据映射’界面处理后的数据进行信号筛选，主要功能包括：

* 提供信号（列名）的搜索和筛选功能

* 支持信号的选择/反选操作

* 保存和加载筛选配置

* 导出筛选后的数据文件



### 1.5.2 功能模块设计



1. 界面管理模块

```python
def __init__(self, root):
    """初始化界面组件"""
    # 创建搜索框和按钮
    # 创建文件操作按钮
    # 创建配置操作按钮
    # 创建滚动区域
    # 绑定鼠标滚轮事件
```



* 搜索与筛选模块

```python
def search_function(self):
    """根据关键词筛选信号"""
    # 实时搜索（KeyRelease事件绑定）
    # 不区分大小写匹配

def show_selected_only(self):
    """只显示已勾选的信号"""

def show_all(self):
    """显示所有信号"""

def filter_checkboxes(self):
    """根据当前筛选条件更新复选框显示"""
```



* &#x20;配置管理模块

```python
def save_config(self):
    """保存当前选择的信号配置到JSON文件"""

def load_config(self):
    """从JSON文件加载信号配置"""
```



* 数据导出模块

```python
def save_data(self):
    """保存筛选后的数据文件"""
    # 在原文件同目录下创建新文件
    # 文件名添加"_selected"后缀
    # 使用utf_8_sig编码避免中文乱码
```



### 1.5.3 系统架构设计



1. 分层架构

```plain&#x20;text
graph TD
    A[用户界面层] --> B[控制逻辑层]
    B --> C[数据处理层]
    
    subgraph A[用户界面层]
        A1[主窗口]
        A2[搜索区域]
        A3[滚动复选框区域]
        A4[按钮面板]
    end
    
    subgraph B[控制逻辑层]
        B1[搜索筛选控制]
        B2[配置管理控制]
        B3[数据导出控制]
    end
    
    subgraph C[数据处理层]
        C1[CSV文件读取]
        C2[列名合并处理]
        C3[数据筛选导出]
        C4[配置持久化]
    end
```



* 核心类设计



**App类（主应用）**

| 属性                 | 说明                 |
| ------------------ | ------------------ |
| `checkbox_vars`    | 复选框状态字典（列名:IntVar） |
| `checkbox_widgets` | 复选框组件列表            |
| `file_paths`       | 原始文件路径列表           |



| 方法                  | 说明     |
| ------------------- | ------ |
| `search_function()` | 执行信号搜索 |
| `save_config()`     | 保存当前配置 |
| `load_config()`     | 加载配置   |
| `save_data()`       | 导出筛选数据 |



### 1.5.4 关键算法分析



1. 动态筛选算法

```python
def filter_checkboxes(self):
    # 隐藏所有复选框
    for cb in self.checkbox_widgets:
        cb.grid_remove()
    
    # 应用当前筛选条件
    filtered_columns = [col for col in self.original_columns if self.filter(col)]
    
    # 显示筛选后的复选框
    for idx, col in enumerate(filtered_columns):
        cb = self.checkbox_widgets[self.original_columns.index(col)]
        cb.grid(row=idx, column=0, sticky='w')
    
    # 更新滚动区域
    self.canvas.configure(scrollregion=self.canvas.bbox("all"))
```



* 文件编码自动检测

```python
try:
    df = pd.read_csv(path, encoding='utf_8_sig')
except UnicodeDecodeError:
    try:
        df = pd.read_csv(path, encoding='gbk')
    except UnicodeDecodeError:
        try:
            df = pd.read_csv(path, encoding='latin-1')
        except UnicodeDecodeError:
            # 处理无法解码的文件
```



* 配置文件管理

```python
# 保存配置
selected = [col for col in self.original_columns if self.checkbox_vars[col].get()]
with open(config_path, 'w') as f:
    json.dump(selected, f)

# 加载配置
with open(config_path, 'r') as f:
    selected = json.load(f)
for col in self.original_columns:
    self.checkbox_vars[col].set(1 if col in selected else 0)
```



### 1.5.5 数据处理流程

```plain&#x20;text
graph LR
    C[显示所有信号] --> D{用户操作}
    D -->|搜索| E[筛选信号]
    D -->|选择信号| F[保存配置]
    D -->|导出数据| G[生成筛选文件]
    F --> H[加载配置]
    H --> C
```



### 1.5.6 核心功能实现



1. 滚动区域实现

```python
# 创建Canvas和Scrollbar
self.canvas = tk.Canvas(self.frame)
scrollbar = ttk.Scrollbar(self.frame, orient="vertical", command=self.canvas.yview)
self.scroll_area = ttk.Frame(self.canvas)

# 配置滚动区域
self.scroll_area.bind("<Configure>", lambda e: self.canvas.configure(scrollregion=self.canvas.bbox("all")))
self.canvas.create_window((0, 0), window=self.scroll_area, anchor='nw')
self.canvas.configure(yscrollcommand=scrollbar.set)

# 绑定鼠标滚轮事件
self.canvas.bind("<MouseWheel>", self.on_mousewheel)
```



* 文件导出实现

```python
# 遍历所有加载的文件
for df, original_path in zip(self.dfs, self.file_paths):
    # 构建新文件路径
    dir_path = os.path.dirname(original_path)
    base_name = os.path.splitext(os.path.basename(original_path))[0]
    new_path = os.path.join(dir_path, f"{base_name}_selected.csv")
    
    # 导出筛选后的数据
    df[selected].to_csv(new_path, index=False, encoding='utf_8_sig')
```



* 实时搜索功能

```python
# 绑定键盘事件实现实时搜索
self.search_entry.bind('<KeyRelease>', lambda event: self.search_function())

def search_function(self):
    keyword = self.search_entry.get().lower()
    filter_condition = lambda col: keyword in col.lower()
    self.filter = filter_condition
    self.filter_checkboxes()
```



### 1.5.7 设计特点

1. 智能编码处理

* 三重编码尝试（utf\_8\_sig, gbk, latin-1）

* 友好的错误提示

* 支持中文路径和内容



* 灵活的筛选机制

- 关键词实时搜索

- 显示已选信号

- 显示所有信号

- 配置保存与加载



* 用户友好的界面

- 可滚动的复选框区域

- 鼠标滚轮支持

- 清晰的按钮布局

- 状态反馈和错误提示



### 1.5.8 使用场景



典型工作流程

1. **筛选信号**：

   * 使用搜索框查找特定信号

   * 勾选需要保留的信号

   * 可保存当前选择为配置

2. **导出数据**：

   * 点击"保存筛选后的数据"

   * 程序自动为每个文件生成筛选后的副本

   * 新文件名添加"\_selected"后缀





## 1.6 数据分析页面（用于通过图表和曲线展示数据）

\#该功能先不开发，仅保留按钮。



## 1.7 导出充电数据报告（用于通过点击按钮，生成期望的充电数据报告）

### 1.7.1 系统概述

本界面是一个用于电池数据分析的界面，主要功能包括：

* 根据‘导入数据功能’加载的数据CSV文件以及‘数据映射’界面处理后的数据

* 通过点击界面上的数据处理按钮自动分析数据

* 计算电池关键性能指标（电压/温度极值等）

* 分析SOC（State of Charge）从98%到100%的充电时间

* 生成分段统计报告和绘图数据

* 保存分析结果



### 1.7.2 功能模块设计

1. 数据处理模块

```python
def process_data(df_B):
    """核心数据处理流程"""
    # 计算电池单体电压极值（过滤异常值）
    # 计算温度传感器极值（过滤异常值）
    # 替换缺失的电压极值列
    # 过滤无效数据行
    # 计算衍生指标（电压差、温度差、功率等）
```



* 时间差计算模块

```python
def calculate_time_differences(df_B):
    """计算SOC从98到99和99到100的时间差"""
    # 定位SOC≥98%的起始点
    # 计算SOC从98%到99%的时间差
    # 计算SOC从99%到100%的时间差
```



* 结果生成模块

```python
def generate_results(df_B, time_difference_1, time_difference_2):
    """生成分段统计结果"""
    # 按SOC每10%分段
    # 计算每段的起始值、结束值、最大值
    # 添加时间差列
    # 补充缺失的行
```



* 结果保存模块

```python
def save_results(results_df, df_B):
    """保存结果到CSV文件"""
    # 保存统计结果（转置格式）
    # 保存绘图数据（精简格式）
```



### 1.7.3 关键算法分析



1. 极值计算算法

```python
# 电池单体电压极值计算（过滤>60V的异常值）
cellu_columns = [col for col in df_B.columns if 'CellU' in col]
if cellu_columns:
    df_cellu = df_B[cellu_columns].applymap(lambda x: np.nan if x > 60 else x)
    df_B['BMS_HvBattCellU_max'] = df_cellu.max(axis=1)
    df_B['BMS_HvBattCellU_min'] = df_cellu.min(axis=1)
```



* SOC时间差计算

```python
# 计算SOC从98%到99%的时间差
if not df_B[df_B['SOC'] >= 98].empty:
    first_row = df_B[df_B['SOC'] >= 98].iloc[0]  # SOC≥98%的第一行
    last_row = df_B[df_B['SOC'] < 99].iloc[-1]   # SOC<99%的最后一行
    time_difference_1 = last_row['time'] - first_row['time']
```



* 分段统计生成

```python
# 按每10% SOC分段统计
curr_soc = 0
while curr_soc < max(df_B['SOC']):
    # 获取当前SOC区间的数据
    soc_range = df_B.loc[(df_B['SOC'] >= curr_soc) & (df_B['SOC'] < curr_soc + 10)]
    
    # 计算每个指标的起始值、结束值、最大值
    row = []
    for column in columns:
        column_values = soc_range[column]
        start = column_values.iloc[0] if len(column_values) > 0 else None
        end = column_values.iloc[-1] if len(column_values) > 0 else None
        max_value = column_values.max() if len(column_values) > 0 else None
        row.extend([start, end, max_value])
    
    results.append(row)
    curr_soc += 10
```



### 1.7.4 数据处理流程



```plain&#x20;text
graph TD
    A[加载数据] --> B[列名重命名]
    B --> C[数据处理]
    C --> D[时间差计算]
    D --> E[生成结果]
    E --> F[保存结果]
    
    subgraph 数据处理
        C1[计算单体电压极值]
        C2[计算温度极值]
        C3[替换电压列]
        C4[过滤无效行]
        C5[计算衍生指标]
    end
    
    subgraph 结果生成
        E1[按SOC分段]
        E2[计算统计值]
        E3[添加时间差]
        E4[补充空行]
    end
```



### 1.7.5 系统架构设计



1. 数据流架构

```plain&#x20;text
graph LR
    U[用户] -->|选择文件| G[GUI界面]
    G --> P[数据处理]
    P -->|处理后数据| T[时间差计算]
    T -->|时间差数据| GR[结果生成]
    GR -->|统计结果| S[结果保存]
    S -->|CSV文件| U
```



* 关键数据结构



**用户数据 (df\_B)**

| 列名                    | 描述      | 计算逻辑                         |
| --------------------- | ------- | ---------------------------- |
| CellU\*               | 电池单体电压  | 原始数据                         |
| TempSensorTemp\*      | 温度传感器数据 | 原始数据                         |
| BMS\_HvBattCellU\_max | 单体电压最大值 | `max(过滤后的CellU)`             |
| BMS\_HvBattCellU\_min | 单体电压最小值 | `min(过滤后的CellU)`             |
| CellVoltage\_Max      | 最大电压    | 替换或重命名                       |
| CellVoltage\_Min      | 最小电压    | 替换或重命名                       |
| CellVoltage\_diff     | 电压差     | `abs(Max - Min)`             |
| Temp\_diff            | 温度差     | `abs(BattT_Max - BattT_Min)` |
| POWER                 | 电池功率    | `-电流 * 电压 / 1000`            |



**结果数据 (results\_df)**

| 统计类型   | 描述             |
| ------ | -------------- |
| start  | 分段起始值          |
| end    | 分段结束值          |
| max    | 分段最大值          |
| 98-99  | SOC 98-99%时间差  |
| 99-100 | SOC 99-100%时间差 |



### 1.7.6 核心功能实现



1. 数据加载与验证

```python
# 检查必要的SOC列
if 'SD7_BBAT_SOC_HVS' not in df_B.columns:
    # 检查备选列
    if 'BMS_SOCDis' in df_B.columns:
        # 询问用户是否使用备选列
        use_bms_socdis = messagebox.askyesno(...)
        if use_bms_socdis:
            df_B['SD7_BBAT_SOC_HVS'] = df_B['BMS_SOCDis'] 
```



* 电压极值列替换

```python
# 替换缺失或无效的电压极值列
if 'CellVoltage_Max' not in df_B.columns or df_B['CellVoltage_Max'].eq(0).all():
    if 'BMS_HvBattCellU_max' in df_B.columns:
        # 备份原列
        df_B.rename(columns={'CellVoltage_Max': 'CellVoltage_Max_old'}, inplace=True)
        # 使用计算值替换
        df_B['CellVoltage_Max'] = df_B['BMS_HvBattCellU_max']
```



* 数据过滤

```python
# 过滤无效数据行
df_B = df_B[df_B['Batt_Current_req'] < 0]  # 只保留电流请求为负值(充电)的行
df_B = df_B[df_B['CellVoltage_Max'] != 0]  # 删除最大电压为零的行
```



* 衍生指标计算

```python
# 创建时间序列
df_B['time'] = range(len(df_B))

# 计算差异指标
df_B['CellVoltage_diff'] = abs(df_B['CellVoltage_Max'] - df_B['CellVoltage_Min'])
df_B['Temp_diff'] = abs(df_B['BattT_Max'] - df_B['BattT_Min'])
df_B['TempWater_diff'] = abs(df_B['Outlet'] - df_B['Inlet'])

# 计算功率
df_B['POWER'] = -df_B['Batt_Current'] * df_B['Batt_Voltage'] / 1000
```



### 1.7.7 设计特点



1. 灵活的列名处理

* **自动重命名**：基于‘数据映射’自动匹配列名

* **备选列支持**：智能处理缺失的SOC列

* **用户选择**：允许用户选择不同的信号源(E0X/EH3)



* 鲁棒的数据处理

- **异常值过滤**：自动排除电压>60V、温度>100℃的异常值

- **缺失列处理**：当标准电压列缺失时，使用计算值替代

- **数据验证**：检查关键列的可用性



* 分段统计分析

- **SOC分段**：每10% SOC一个分析区间

- **三重统计**：记录各段的起始值、结束值、最大值

- **时间差分析**：特别关注98-100% SOC的充电时间



* 双输出模式

- **统计报告**：转置格式便于查看

- **绘图数据**：精简格式便于可视化分析



### 1.7.8 典型工作流程

1. **进入‘导出充电数据报告’界面**：

   * 点击数据处理按钮

2. **数据处理**：

   * 自动重命名列

   * 计算电压/温度极值

   * 过滤无效数据

3) **结果生成**：

   * 计算SOC关键区间时间差

   * 生成分段统计报告

4) **结果保存**：

   * 保存统计报告

   * 保存绘图数据

##



## 2. 技术栈

* **GUI框架**: Tkinter 或pyqt 或其他python支持的界面显示工具

* **数据处理：Pandas + NumPy**

* **图像处理**: Pillow (PIL)

* **数据序列化**: JSON

* **架构模式**: MVC (Model-View-Controller)

* **可视化**: Matplotlib (潜在扩展)

* **可视化**：Matplotlib

* **多线程**：threading

* **文件操作**：标准库io

* **数据持久化**：CSV

* **配置管理**：JSON

* **文件操作**：os模块



## 3. 其他需求

* 每个页面都要有返回主页面的按钮，便于返回主页面

* 每个页面进行数据进一步处理时，必须要先依据‘数据映射’后的数据来处理

* 页面排版合理，无重叠，布局合理整齐。

* 页面功能不重复，导入测试数据仅由导入数据功能来实现，导入充电基准文件可由充电分析界面的按钮实现。

* 支持扩展按钮和其他功能页面的部署

*

***

增加功能

1.









# 参考图片：

## 一. 主界面：

![](images/4fcb52ae-3dcf-4d62-ba60-b6aaaf0e5fd6.jpeg)



## 二. 数据筛选：

![](images/67ce1203-a7b8-46b2-9d71-47c7ab22ca19.jpeg)



## 三. 数据映射：

![](images/e68451cb-3598-41f4-a279-cf124381fcbb.jpeg)

## 四. 充电分析

![](images/965a7d0d-0451-416b-adea-3a3e17a757fb.jpeg)



## 五. 温感布置

![](images/00bb1a38-5de1-4ca2-89bf-27b9f156be79.jpeg)

## 六. 导出充电数据报告

![](images/72686937-2cb8-4bd5-9ed4-e72279242fca.jpeg)

## 七. 数据分析





# Program

```python
```
