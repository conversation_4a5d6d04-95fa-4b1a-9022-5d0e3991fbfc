import tkinter as tk
from tkinter import ttk, filedialog, messagebox, simpledialog
import pandas as pd
from PIL import Image, ImageTk
import json
import os

# 定义一个颜色梯度函数，根据值的大小返回颜色
def get_color_for_value(value, min_val, max_val):
    """根据值在最小最大值之间的位置，从蓝到红生成颜色"""
    if value is None:
        return "#808080" # Gray for no value
    if pd.isna(value): # Explicitly check for pandas NaN
        return "#808080"
    if max_val == min_val: # Handles single point or all points having same value
        return "#0000FF" if value is not None else "#808080" # Blue if value exists, else gray
    # Ensure min_val and max_val are not None before normalization
    if pd.isna(min_val) or pd.isna(max_val):
        return "#808080"

    normalized = (value - min_val) / (max_val - min_val)
    red = int(normalized * 255)
    blue = int((1 - normalized) * 255)
    return f'#{red:02x}00{blue:02x}'

class MappingWindow(tk.Toplevel):
    def __init__(self, parent, current_mapping, headers, current_time_column=None):
        super().__init__(parent)
        self.transient(parent)
        self.parent = parent
        self.title("数据映射配置")
        self.geometry("550x500")
        self.minsize(450, 350)

        self.headers = headers
        self.current_time_column = current_time_column
        self.mapping_vars = []

        self.grid_rowconfigure(2, weight=1)
        self.grid_columnconfigure(0, weight=1)

        time_select_frame = ttk.Frame(self, padding="5")
        time_select_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=5)
        ttk.Label(time_select_frame, text="时间列 (可选):").pack(side='left', padx=(0, 5))
        self.time_column_var = tk.StringVar()
        if self.current_time_column and self.current_time_column in self.headers:
            self.time_column_var.set(self.current_time_column)

        time_column_options = [""] + self.headers
        self.time_column_menu = ttk.Combobox(time_select_frame, textvariable=self.time_column_var,
                                             values=time_column_options, width=30, state="readonly")
        self.time_column_menu.pack(side='left', padx=5, fill='x', expand=True)

        controls_frame = ttk.Frame(self, padding="5")
        controls_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=5)
        ttk.Button(controls_frame, text="增加映射", command=self.add_row).pack(side='left', padx=5)
        ttk.Button(controls_frame, text="删除最后一行", command=self.remove_row).pack(side='left', padx=5)

        middle_frame = ttk.Frame(self)
        middle_frame.grid(row=2, column=0, sticky="nsew", padx=10)
        middle_frame.grid_rowconfigure(0, weight=1)
        middle_frame.grid_columnconfigure(0, weight=1)

        self.canvas = tk.Canvas(middle_frame, borderwidth=0, background="#ffffff")
        self.scrollable_frame = ttk.Frame(self.canvas)
        scrollbar = ttk.Scrollbar(middle_frame, orient="vertical", command=self.canvas.yview)
        self.canvas.configure(yscrollcommand=scrollbar.set)

        scrollbar.grid(row=0, column=1, sticky='ns')
        self.canvas.grid(row=0, column=0, sticky='nsew')
        self.canvas_window = self.canvas.create_window((0, 0), window=self.scrollable_frame, anchor="nw")

        self.scrollable_frame.bind("<Configure>", self.on_frame_configure)
        self.canvas.bind("<Configure>", self.on_canvas_configure)

        bottom_frame = ttk.Frame(self, padding="5")
        bottom_frame.grid(row=3, column=0, sticky="ew", padx=10, pady=10)
        bottom_frame.grid_columnconfigure(0, weight=1)
        bottom_frame.grid_columnconfigure(1, weight=1)
        bottom_frame.grid_columnconfigure(2, weight=1)

        ttk.Button(bottom_frame, text="导入配置", command=self.load_config).grid(row=0, column=0, sticky='w')
        ttk.Button(bottom_frame, text="保存配置", command=self.save_config).grid(row=0, column=1)
        ttk.Button(bottom_frame, text="应用并返回", command=self.apply_and_close).grid(row=0, column=2, sticky='e')

        self.load_initial_mapping(current_mapping)

    def on_frame_configure(self, event): self.canvas.configure(scrollregion=self.canvas.bbox("all"))
    def on_canvas_configure(self, event): self.canvas.itemconfig(self.canvas_window, width=event.width)

    def load_initial_mapping(self, mapping):
        for csv_header, point_name in mapping.items(): self.add_row(csv_header, point_name)
        if not self.mapping_vars:
            self.add_row()

    def add_row(self, csv_header_val=None, point_name_val=""):
        row_frame = ttk.Frame(self.scrollable_frame, padding=2)
        row_frame.pack(fill='x', expand=True, pady=2)
        ttk.Label(row_frame, text="CSV列:").pack(side='left', padx=(0, 5))
        header_var = tk.StringVar()
        if csv_header_val and csv_header_val in self.headers: header_var.set(csv_header_val)
        header_menu = ttk.Combobox(row_frame, textvariable=header_var, values=self.headers, width=20, state="readonly")
        header_menu.pack(side='left', padx=5, fill='x', expand=True)
        ttk.Label(row_frame, text="温度点名称:").pack(side='left', padx=(10, 5))
        point_var = tk.StringVar(value=point_name_val)
        point_entry = ttk.Entry(row_frame, textvariable=point_var, width=20)
        point_entry.pack(side='left', padx=5, fill='x', expand=True)
        self.mapping_vars.append({'frame': row_frame, 'header': header_var, 'point': point_var})

    def remove_row(self):
        if self.mapping_vars:
            last_map = self.mapping_vars.pop()
            last_map['frame'].destroy()

    def apply_and_close(self):
        new_field_mapping = {item['header'].get(): item['point'].get() for item in self.mapping_vars if item['header'].get() and item['point'].get()}
        selected_time_col = self.time_column_var.get()
        self.parent.update_mapping(new_field_mapping, selected_time_col if selected_time_col else None)
        self.destroy()

    def save_config(self):
        field_mappings = {item['header'].get(): item['point'].get() for item in self.mapping_vars if item['header'].get() and item['point'].get()}
        config_to_save = {
            'field_mappings': field_mappings,
            'time_column': self.time_column_var.get() or None
        }
        filepath = filedialog.asksaveasfilename(defaultextension=".json", filetypes=[("JSON files", "*.json")], title="保存映射配置")
        if filepath:
            try:
                with open(filepath, 'w', encoding='utf-8') as f: json.dump(config_to_save, f, indent=4)
                messagebox.showinfo("成功", "映射配置已成功保存！")
            except Exception as e: messagebox.showerror("错误", f"保存映射配置失败: {e}")

    def load_config(self):
        filepath = filedialog.askopenfilename(filetypes=[("JSON files", "*.json")], title="导入映射配置")
        if filepath:
            try:
                with open(filepath, 'r', encoding='utf-8') as f: loaded_data = json.load(f)
                field_mappings_to_load = {}
                time_col_to_load = ""
                if isinstance(loaded_data, dict) and 'field_mappings' in loaded_data:
                    field_mappings_to_load = loaded_data.get('field_mappings', {})
                    time_col_to_load = loaded_data.get('time_column') or ""
                elif isinstance(loaded_data, dict):
                    field_mappings_to_load = loaded_data

                while self.mapping_vars: self.remove_row()
                self.load_initial_mapping(field_mappings_to_load)

                if time_col_to_load and time_col_to_load in self.headers:
                    self.time_column_var.set(time_col_to_load)
                else:
                    self.time_column_var.set("")
                messagebox.showinfo("成功", "映射配置已成功导入！")
            except Exception as e: messagebox.showerror("错误", f"导入映射配置失败: {e}")


class MainApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("电池温度变化趋势分析工具")
        self.geometry("1200x800")

        self.df = None; self.mapping = {}; self.temp_points = {}
        self.image_path = None; self.image_tk = None; self.original_image = None
        self.zoom_factor = 1.0; self.csv_filepath = None; self.time_column_header = None
        self.display_mode = "delta_t"; self.current_time_index = 0
        self.is_slider_active = False
        self.current_slider_min_max_for_color = (0,0)
        self.selected_point_name_in_tree = None # For point deletion

        self.is_insert_mode = False; self._drag_data = {"x": 0, "y": 0, "item": None}

        top_frame = ttk.Frame(self, padding="10")
        top_frame.pack(side='top', fill='x', pady=(0,5))

        ttk.Button(top_frame, text="导入数据 (.csv)", command=self.import_data).pack(side='left', padx=5)
        self.map_button = ttk.Button(top_frame, text="数据映射", command=self.open_mapping_window, state='disabled')
        self.map_button.pack(side='left', padx=5)
        ttk.Button(top_frame, text="导入图片", command=self.import_image).pack(side='left', padx=5)
        self.insert_button = ttk.Button(top_frame, text="插入温度点", command=self.toggle_insert_mode, state='disabled')
        self.insert_button.pack(side='left', padx=5)
        self.delete_point_button = ttk.Button(top_frame, text="删除选定点", command=self.delete_selected_point, state='disabled')
        self.delete_point_button.pack(side='left', padx=5)

        self.delta_t_button = ttk.Button(top_frame, text="显示温差", command=lambda: self.set_summary_display_mode("delta_t"), state='disabled')
        self.delta_t_button.pack(side='left', padx=5)
        self.max_val_button = ttk.Button(top_frame, text="显示最大值", command=lambda: self.set_summary_display_mode("max_val"), state='disabled')
        self.max_val_button.pack(side='left', padx=5)
        self.min_val_button = ttk.Button(top_frame, text="显示最小值", command=lambda: self.set_summary_display_mode("min_val"), state='disabled')
        self.min_val_button.pack(side='left', padx=5)

        ttk.Button(top_frame, text="保存项目", command=self.save_project).pack(side='left', padx=5)
        ttk.Button(top_frame, text="加载项目", command=self.load_project).pack(side='left', padx=5)

        main_frame = ttk.Frame(self)
        main_frame.pack(fill="both", expand=True)

        self.canvas = tk.Canvas(main_frame, bg="lightgrey")
        self.canvas.pack(side="left", fill="both", expand=True)

        right_panel = ttk.Frame(main_frame, width=280)
        right_panel.pack(side="right", fill="y", padx=(5,0))
        self.results_label = ttk.Label(right_panel, text="结果显示", font=("Arial", 12, "bold"))
        self.results_label.pack(pady=(5,10))

        self.results_tree = ttk.Treeview(right_panel, columns=("Point", "Value"), show="headings")
        self.results_tree.heading("Point", text="温度点名称")
        self.results_tree.heading("Value", text="温差 (°C)")
        self.results_tree.column("Point", width=130)
        self.results_tree.column("Value", width=100, anchor='e')
        self.results_tree.pack(fill="both", expand=True)
        self.results_tree.bind('<<TreeviewSelect>>', self.on_tree_point_select)

        bottom_frame = ttk.Frame(self, padding="5")
        bottom_frame.pack(side='bottom', fill='x', pady=(5,0))
        self.time_label = ttk.Label(bottom_frame, text="Time: N/A", width=30)
        self.time_label.pack(side='left', padx=5)
        self.time_slider = ttk.Scale(bottom_frame, from_=0, to=0, orient=tk.HORIZONTAL, command=self.on_time_slider_change, state='disabled')
        self.time_slider.pack(side='left', fill='x', expand=True, padx=5)

        self.canvas.bind("<MouseWheel>", self.zoom_image); self.canvas.bind("<Button-4>", self.zoom_image); self.canvas.bind("<Button-5>", self.zoom_image)
        self.canvas.bind("<ButtonPress-1>", self.on_press); self.canvas.bind("<B1-Motion>", self.on_drag); self.canvas.bind("<ButtonRelease-1>", self.on_release)

        self._update_ui_states()

    def _update_ui_states(self):
        data_ready = self.df is not None and bool(self.temp_points) and bool(self.mapping)
        summary_buttons_state = 'normal' if data_ready else 'disabled'
        self.delta_t_button.config(state=summary_buttons_state)
        self.max_val_button.config(state=summary_buttons_state)
        self.min_val_button.config(state=summary_buttons_state)

        self.insert_button.config(state='normal' if self.original_image else 'disabled')
        self.map_button.config(state='normal' if self.df is not None else 'disabled')

        time_display_text = f"Row: {self.current_time_index}"
        if self.df is not None and len(self.df) > 0: # Check if df has rows
            if len(self.df) > 1: self.time_slider.config(state='normal', to=len(self.df) - 1)
            else: self.time_slider.config(state='disabled', to=0, value=0) # Single row df

            if self.time_column_header and self.time_column_header in self.df.columns:
                if self.current_time_index < len(self.df):
                    time_val = self.df[self.time_column_header].iloc[self.current_time_index]
                    time_display_text = f"{self.time_column_header}: {time_val}"
                else: time_display_text = f"{self.time_column_header}: Out of bounds"
            elif self.time_column_header:
                 time_display_text = f"{self.time_column_header}: Not in data"
        else:
            self.time_slider.config(state='disabled', to=0, value=0)
            time_display_text = "Time: N/A"

        self.time_label.config(text=time_display_text)


    def import_data(self):
        filepath = filedialog.askopenfilename(filetypes=[("CSV files", "*.csv")])
        if not filepath: return
        try:
            self.df = pd.read_csv(filepath)
            self.csv_filepath = filepath
            self.current_time_index = 0
            messagebox.showinfo("成功", f"成功导入数据！\n列名: {', '.join(self.df.columns)}")
        except Exception as e:
            messagebox.showerror("错误", f"无法读取CSV文件: {e}"); self.df = None; self.csv_filepath = None

        if self.df is None or (self.time_column_header and self.time_column_header not in self.df.columns):
            self.time_column_header = None

        self._update_ui_states()
        if self.df is not None: self.set_summary_display_mode(self.display_mode, force_recalc=True)
        else: self.clear_calculated_data(update_points_visuals=True)


    def open_mapping_window(self):
        headers = list(self.df.columns) if self.df is not None else []
        win = MappingWindow(self, self.mapping, headers, self.time_column_header)
        win.grab_set()

    def update_mapping(self, new_mapping, time_column_header=None):
        self.mapping = new_mapping
        self.time_column_header = time_column_header
        print("Updated mapping:", self.mapping)
        print("Updated time column:", self.time_column_header)
        self._update_ui_states()
        if self.df is not None and self.temp_points:
            self.set_summary_display_mode(self.display_mode, force_recalc=True)
            if self.is_slider_active:
                self.update_display_for_slider()


    def import_image(self):
        filepath = filedialog.askopenfilename(filetypes=[("Image files", "*.jpg *.jpeg *.png *.bmp *.gif")]) # Added GIF
        if not filepath:
            return

        try:
            # Try to open the image to verify it's a valid image file
            # self.original_image will be set here if successful
            img = Image.open(filepath)
            # Ensure it's converted to RGB if it's a palette-based image like some GIFs,
            # as some PIL operations might behave unexpectedly otherwise.
            # However, PhotoImage handles various modes, so direct conversion might not always be needed
            # For now, let's keep it simple unless specific issues arise with point drawing or display.
            # if img.mode not in ['RGB', 'RGBA']:
            #    img = img.convert('RGBA' if img.info.get('transparency') is not None else 'RGB')

            self.original_image = img
            self.image_path = filepath
            self.zoom_factor = 1.0  # Reset zoom for new image

            # Crucially, self.temp_points is NOT cleared here.

            self.display_image()  # This will clear canvas and redraw new image + existing points
            self.insert_button.config(state='normal') # Enable point insertion

        except FileNotFoundError:
            messagebox.showerror("错误", f"图片文件未找到: {filepath}")
            self.image_path = None # Reset path
            self.original_image = None # Reset image object
            # self.display_image() # Optionally clear canvas if an old image was there
            # No, display_image() without original_image will clear and try to draw points, which is fine.
        except (Image.UnidentifiedImageError, Exception) as e: # Catch PIL errors and other generic exceptions
            messagebox.showerror("错误", f"无法加载或识别图片: {filepath}\n{e}")
            self.image_path = None
            self.original_image = None
            # self.display_image()

        self._update_ui_states() # Update button states based on whether an image is loaded


    def display_image(self):
        self.canvas.delete("all")
        if self.original_image:
            w, h = self.original_image.size; new_size = (int(w * self.zoom_factor), int(h * self.zoom_factor))
            try:
                self.image_tk = ImageTk.PhotoImage(self.original_image.resize(new_size, Image.Resampling.LANCZOS))
                self.canvas.create_image(0, 0, anchor="nw", image=self.image_tk, tags="bg_image")
                self.canvas.config(scrollregion=self.canvas.bbox("all"))
            except Exception as e: messagebox.showerror("错误", f"图像缩放时出错: {e}"); self.image_tk = None
        for name in self.temp_points.keys(): self.draw_point(name)

    def zoom_image(self, event):
        factor = 1.1 if (event.delta > 0 or event.num == 4) else (1 / 1.1)
        self.zoom_factor = max(0.05, min(self.zoom_factor * factor, 10.0))
        self.display_image()

    def toggle_insert_mode(self):
        self.is_insert_mode = not self.is_insert_mode
        self.insert_button.config(relief="sunken" if self.is_insert_mode else "raised")
        self.config(cursor="crosshair" if self.is_insert_mode else "")

    def on_press(self, event):
        canvas_x, canvas_y = self.canvas.canvasx(event.x), self.canvas.canvasy(event.y)
        if self.is_insert_mode:
            self.add_point_at(canvas_x / self.zoom_factor, canvas_y / self.zoom_factor)
            self.toggle_insert_mode()
        else:
            items = self.canvas.find_overlapping(canvas_x-1,canvas_y-1,canvas_x+1,canvas_y+1)
            item = next((i for i in items if "temp_point" in self.canvas.gettags(i)), None)
            if item: self._drag_data = {"item": item, "x": event.x, "y": event.y}
            else: self._drag_data["item"] = None

    def on_drag(self, event):
        if self._drag_data.get("item"):
            dx, dy = event.x - self._drag_data["x"], event.y - self._drag_data["y"]
            tag = next((t for t in self.canvas.gettags(self._drag_data["item"]) if t.startswith("point_")), None)
            if tag: self.canvas.move(tag, dx, dy)
            self._drag_data["x"], self._drag_data["y"] = event.x, event.y

    def on_release(self, event):
        item = self._drag_data.get("item")
        if item:
            self._drag_data["item"] = None
            tag = next((t for t in self.canvas.gettags(item) if t.startswith("point_")), None)
            if tag:
                name = tag.replace("point_", "")
                if name in self.temp_points and self.temp_points[name].get('id'):
                    oval_id = self.temp_points[name]['id'][0]
                    coords = self.canvas.coords(oval_id)
                    if not coords: return
                    cx, cy = (coords[0]+coords[2])/2, (coords[1]+coords[3])/2
                    img_x, img_y = cx/self.zoom_factor, cy/self.zoom_factor
                    if self.original_image:
                        img_w, img_h = self.original_image.size
                        img_x, img_y = max(0, min(img_x, img_w)), max(0, min(img_y, img_h))
                    self.temp_points[name]['coords'] = (img_x, img_y)
                    self.draw_point(name)

    def add_point_at(self, x, y):
        name = simpledialog.askstring("输入", "请输入温度点名称:", parent=self)
        if not name: return
        if name in self.temp_points: messagebox.showerror("错误", "该温度点名称已存在!"); return
        self.temp_points[name] = {'coords': (x,y), 'last_calculated_value': None, 'last_display_prefix': '', 'slider_value': None}
        self.draw_point(name)
        self._update_ui_states()

    def draw_point(self, name):
        point_data = self.temp_points.get(name)
        if not point_data or 'coords' not in point_data: return

        x, y = point_data['coords']
        value_to_display, prefix_to_display = None, ""
        min_c, max_c = 0,0

        if self.is_slider_active:
            value_to_display = point_data.get('slider_value')
            prefix_to_display = "T"
            min_c, max_c = self.current_slider_min_max_for_color
        else:
            value_to_display = point_data.get('last_calculated_value')
            prefix_to_display = point_data.get('last_display_prefix', '')
            all_summary_values = [p.get('last_calculated_value') for p in self.temp_points.values() if p.get('last_calculated_value') is not None and not pd.isna(p.get('last_calculated_value'))]
            if all_summary_values: min_c, max_c = min(all_summary_values), max(all_summary_values)
            else: min_c, max_c = (value_to_display, value_to_display) if value_to_display is not None and not pd.isna(value_to_display) else (0,0)

        color = get_color_for_value(value_to_display, min_c, max_c)
        text_content = name
        if value_to_display is not None and not pd.isna(value_to_display):
            text_content = f"{name}\n{prefix_to_display}: {value_to_display:.2f}"

        scaled_x, scaled_y, radius = x * self.zoom_factor, y * self.zoom_factor, 8
        point_tag = f"point_{name}"

        if 'id' in point_data and point_data['id']:
            try: self.canvas.delete(point_data['id'][0]); self.canvas.delete(point_data['id'][1])
            except tk.TclError: pass

        oval_id = self.canvas.create_oval(scaled_x-radius, scaled_y-radius, scaled_x+radius, scaled_y+radius, fill=color, outline="black", tags=("temp_point", point_tag))
        text_id = self.canvas.create_text(scaled_x, scaled_y+radius+10, text=text_content, anchor="n", tags=("temp_point_text", point_tag))
        point_data['id'] = (oval_id, text_id)


    def set_summary_display_mode(self, mode, force_recalc=False):
        self.is_slider_active = False
        self.display_mode = mode
        header_map = {"delta_t": "温差 (°C)", "max_val": "最大值 (°C)", "min_val": "最小值 (°C)"}
        self.results_tree.heading("Value", text=header_map.get(mode, "数值"))
        self.results_label.config(text=f"数据概览: {header_map.get(mode, '数值')}")

        if not (self.df is not None and bool(self.temp_points) and bool(self.mapping)):
            self.clear_calculated_data(update_points_visuals=True)
            self._update_ui_states(); return

        for i in self.results_tree.get_children(): self.results_tree.delete(i)

        point_to_header = {v: k for k, v in self.mapping.items()}
        prefix_map = {"delta_t": "ΔT", "max_val": "Max", "min_val": "Min"}
        current_prefix = prefix_map.get(mode, "")

        for name, point_data in self.temp_points.items():
            calculated_value = None
            header = point_to_header.get(name)
            if header and header in self.df.columns:
                try:
                    series = self.df[header].dropna().astype(float)
                    if not series.empty:
                        if mode == "delta_t":
                            if len(series) >= 2: calculated_value = series.iloc[-1] - series.iloc[0]
                        elif mode == "max_val": calculated_value = series.max()
                        elif mode == "min_val": calculated_value = series.min()
                except ValueError: print(f"Warning: Non-numeric data in column {header} for point {name}.")
            point_data['last_calculated_value'] = calculated_value
            point_data['last_display_prefix'] = current_prefix

        for name, point_data in self.temp_points.items():
            self.draw_point(name)
            val_for_tree = point_data.get('last_calculated_value')
            self.results_tree.insert("", "end", values=(name, f"{val_for_tree:.2f}" if val_for_tree is not None and not pd.isna(val_for_tree) else "N/A"))

        self._update_ui_states()


    def on_time_slider_change(self, value_str):
        self.is_slider_active = True
        self.current_time_index = int(float(value_str))
        if hasattr(self, 'time_slider'): self.time_slider.set(self.current_time_index)
        self._update_ui_states()
        self.update_display_for_slider()

    def update_display_for_slider(self):
        self.is_slider_active = True
        if not (self.df is not None and bool(self.temp_points) and bool(self.mapping)):
            self.clear_calculated_data(update_points_visuals=True)
            self._update_ui_states(); return

        idx = self.current_time_index

        # Determine the text for results_label (incorporating time column if available)
        results_label_text = f"瞬时温度 @ 时间点 (行索引): {idx}"
        if self.time_column_header and self.time_column_header in self.df.columns and idx < len(self.df):
             time_val = self.df[self.time_column_header].iloc[idx]
             results_label_text = f"瞬时温度 @ {self.time_column_header}: {time_val}"
        self.results_label.config(text=results_label_text)
        self.results_tree.heading("Value", text=f"Temp @ Idx {idx} (°C)")


        for i in self.results_tree.get_children(): self.results_tree.delete(i)

        point_to_header = {v: k for k, v in self.mapping.items()}
        instant_values_for_color_scale = []

        for name, point_data in self.temp_points.items():
            inst_value = None
            header = point_to_header.get(name)
            if header and header in self.df.columns and idx < len(self.df[header]):
                val = self.df[header].iloc[idx]
                if not pd.isna(val):
                    try: inst_value = float(val)
                    except ValueError: inst_value = None
            point_data['slider_value'] = inst_value
            if inst_value is not None and not pd.isna(inst_value): instant_values_for_color_scale.append(inst_value)

        if instant_values_for_color_scale:
            self.current_slider_min_max_for_color = (min(instant_values_for_color_scale), max(instant_values_for_color_scale))
        else:
            self.current_slider_min_max_for_color = (0,0)


        for name, point_data in self.temp_points.items():
            self.draw_point(name)
            val_for_tree = point_data.get('slider_value')
            self.results_tree.insert("", "end", values=(name, f"{val_for_tree:.2f}" if val_for_tree is not None and not pd.isna(val_for_tree) else "N/A"))
        # _update_ui_states() is called by on_time_slider_change already

    def clear_calculated_data(self, update_points_visuals=False):
        for i in self.results_tree.get_children(): self.results_tree.delete(i)
        if update_points_visuals:
            for name, point_data in self.temp_points.items():
                point_data['last_calculated_value'] = None
                point_data['last_display_prefix'] = ''
                point_data['slider_value'] = None
                self.draw_point(name)

    def save_project(self):
        if not any([self.image_path, self.csv_filepath, self.temp_points]):
            messagebox.showwarning("警告", "没有可保存的内容."); return
        filepath = filedialog.asksaveasfilename(defaultextension=".json", filetypes=[("Project files", "*.json")])
        if not filepath: return

        temp_points_data = {name: {
            'coords': data['coords'],
            'last_calculated_value': data.get('last_calculated_value'),
            'last_display_prefix': data.get('last_display_prefix'),
            'slider_value': data.get('slider_value')
        } for name, data in self.temp_points.items()}

        project_data = {
            "image_path": self.image_path, "csv_filepath": self.csv_filepath,
            "temp_points": temp_points_data, "mapping": self.mapping,
            "time_column_header": self.time_column_header, "zoom_factor": self.zoom_factor,
            "display_mode": self.display_mode, "current_time_index": self.current_time_index,
            "is_slider_active": self.is_slider_active
        }
        try:
            with open(filepath, 'w', encoding='utf-8') as f: json.dump(project_data, f, indent=4)
            messagebox.showinfo("成功", "项目已保存。")
        except Exception as e: messagebox.showerror("错误", f"无法保存项目: {e}")

    def load_project(self):
        filepath = filedialog.askopenfilename(filetypes=[("Project files", "*.json")])
        if not filepath: return
        try:
            with open(filepath, 'r', encoding='utf-8') as f: project_data = json.load(f)
        except Exception as e: messagebox.showerror("错误", f"无法加载项目: {e}"); return

        self.canvas.delete("all"); self.clear_calculated_data()
        for i in self.results_tree.get_children(): self.results_tree.delete(i)
        self.df=None; self.mapping={}; self.temp_points={}; self.image_path=None; self.image_tk=None
        self.original_image=None; self.zoom_factor=1.0; self.csv_filepath=None;

        self.time_column_header = project_data.get("time_column_header")
        self.display_mode = project_data.get("display_mode", "delta_t")
        self.current_time_index = project_data.get("current_time_index", 0)
        self.is_slider_active = project_data.get("is_slider_active", False)
        self.zoom_factor = project_data.get("zoom_factor", 1.0)

        if project_data.get("image_path") and os.path.exists(project_data["image_path"]):
            try: self.original_image=Image.open(project_data["image_path"]); self.image_path=project_data["image_path"]
            except Exception as e: messagebox.showerror("错误", f"无法加载图片: {project_data['image_path']}\n{e}")

        if project_data.get("csv_filepath") and os.path.exists(project_data["csv_filepath"]):
            try: self.df=pd.read_csv(project_data["csv_filepath"]); self.csv_filepath=project_data["csv_filepath"]
            except Exception as e: messagebox.showerror("错误", f"无法加载CSV: {project_data['csv_filepath']}\n{e}")

        self.mapping = project_data.get("mapping", {})

        loaded_points = project_data.get("temp_points", {})
        for name, data in loaded_points.items():
            self.temp_points[name] = {
                'coords': tuple(data['coords']),
                'last_calculated_value': data.get('last_calculated_value'),
                'last_display_prefix': data.get('last_display_prefix'),
                'slider_value': data.get('slider_value')
            }

        self._update_ui_states()

        if self.df is not None and len(self.df) > 0: # Check df has rows before setting slider
            if len(self.df) > 1 and hasattr(self, 'time_slider'):
                max_slider_val = len(self.df) -1
                self.current_time_index = min(self.current_time_index, max_slider_val)
                self.time_slider.set(self.current_time_index)
            elif hasattr(self, 'time_slider'): # Single row df
                 self.time_slider.set(0)


        self.display_image()

        if self.is_slider_active and self.df is not None:
            self.update_display_for_slider()
        else:
            self.set_summary_display_mode(self.display_mode, force_recalc=True)

        messagebox.showinfo("成功", "项目已加载。")

    def on_tree_point_select(self, event):
        selected_items = self.results_tree.selection()
        if selected_items:
            selected_item_id = selected_items[0]
            item_values = self.results_tree.item(selected_item_id, 'values')
            if item_values and len(item_values) > 0:
                self.selected_point_name_in_tree = item_values[0]
                self.delete_point_button.config(state='normal')
                print(f"Selected point in tree: {self.selected_point_name_in_tree}")
            else:
                # This case should ideally not be reached if a row is selected but has no values.
                self.selected_point_name_in_tree = None
                self.delete_point_button.config(state='disabled')
        else: # No items selected (e.g., selection cleared)
            self.selected_point_name_in_tree = None
            self.delete_point_button.config(state='disabled')

    def delete_selected_point(self):
        point_to_delete_name = self.selected_point_name_in_tree
        if point_to_delete_name is None:
            messagebox.showwarning("删除点", "没有选择任何点。")
            return

        if point_to_delete_name not in self.temp_points:
            messagebox.showerror("错误", f"点 '{point_to_delete_name}' 在内部数据中未找到。")
            self.selected_point_name_in_tree = None
            self.delete_point_button.config(state='disabled')
            return

        # Proceed with deletion
        point_data = self.temp_points[point_to_delete_name]

        # Remove Canvas Graphics
        if 'id' in point_data and point_data['id'] is not None:
            oval_id, text_id = point_data['id']
            if oval_id: self.canvas.delete(oval_id)
            if text_id: self.canvas.delete(text_id)
        else:
            print(f"Warning: No canvas ID found for point {point_to_delete_name} during deletion.")

        # Remove from self.temp_points Data Structure
        del self.temp_points[point_to_delete_name]

        # Remove from results_tree
        item_to_delete_from_tree = None
        for item_id in self.results_tree.get_children(''):
            item_values = self.results_tree.item(item_id, 'values')
            # Ensure item_values is not empty and is indexable, and is a tuple/list as expected
            if item_values and isinstance(item_values, (tuple, list)) and len(item_values) > 0 and item_values[0] == point_to_delete_name:
                item_to_delete_from_tree = item_id
                break

        if item_to_delete_from_tree:
            self.results_tree.delete(item_to_delete_from_tree)
        else:
            print(f"Warning: Point {point_to_delete_name} not found in results_tree for deletion.")

        # Reset Selection State (important as the selection event might not fire if tree is manipulated directly)
        self.selected_point_name_in_tree = None
        self.delete_point_button.config(state='disabled')

        # Update UI States and Refresh Display
        self._update_ui_states()

        # Refresh display as deletion might affect color scales or summary values
        # Also, if no points are left, calculations should reflect that.
        if not self.temp_points: # If all points deleted
            self.clear_calculated_data(update_points_visuals=True) # Clear tree, reset point visuals (none left but good practice)
            self.results_label.config(text="结果显示") # Reset label
            self.results_tree.heading("Value", text="数值") # Reset tree header
        elif self.is_slider_active:
            self.update_display_for_slider()
        else:
            self.set_summary_display_mode(self.display_mode, force_recalc=True)

        messagebox.showinfo("成功", f"点 '{point_to_delete_name}' 已删除。")


if __name__ == "__main__":
    app = MainApp()
    app.mainloop()
